<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.example</groupId>
    <artifactId>forbidden-apis-check</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 默认签名文件路径，可以通过命令行覆盖 -->
        <signatures.file.path>/Users/<USER>/workspace/fxiaoke/egress-api-service/f.txt</signatures.file.path>
    </properties>
    
    <build>
        <plugins>
            <plugin>
                <groupId>de.thetaphi</groupId>
                <artifactId>forbiddenapis</artifactId>
                <version>3.9</version>
                <configuration>
                    <!-- 如果使用的Java版本太新，不要失败，只是什么都不做 -->
                    <failOnUnsupportedJava>false</failOnUnsupportedJava>
                    
                    <!-- 如果在解析签名文件时缺少类，则忽略该类的所有方法和字段 -->
                    <ignoreSignaturesOfMissingClasses>true</ignoreSignaturesOfMissingClasses>
                    
                    <!-- 指定签名文件 - 可以通过系统属性覆盖 -->
                    <signaturesFiles>
                        <signaturesFile>${signatures.file.path}</signaturesFile>
                    </signaturesFiles>
                    
                    <!-- 可选：添加一些内置签名 -->
                    <bundledSignatures>
                        <bundledSignature>jdk-unsafe</bundledSignature>
                        <bundledSignature>jdk-deprecated</bundledSignature>
                    </bundledSignatures>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
