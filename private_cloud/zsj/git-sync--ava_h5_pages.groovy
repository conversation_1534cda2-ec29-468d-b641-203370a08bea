package private_cloud.zsj
final GIT_URL = "https://git.firstshare.cn/fs-hera/ava_h5_pages"
final GIT_HOOK_TOKEN = "3Sfb912QUuts6AYvJ50XWToOLPzGmrixZIjpcHER8C"
final ZSJ_GIT_URL = "https://13552346839:<EMAIL>/CMHK-CRM-DI/ava-h5-pages.git"

pipeline {
  agent {
    label 'k8s-publish'
  }
  triggers {
    gitlab(
      triggerOnPush: true,
      triggerOnNoteRequest: true,
      noteRegex: "Jenkins please retry a build",
      secretToken: "${GIT_HOOK_TOKEN}"
    )
  }
  stages {
    stage("拉取源码") {
      steps {
        checkout([$class                           : 'GitSCM',
                  branches                         : [[name: "master"]],
                  doGenerateSubmoduleConfigurations: false,
                  extensions                       : [[$class: 'PruneStaleBranch'],
                                                      [$class: 'CheckoutOption', timeout: 120],
                                                      [$class: 'CloneOption', timeout: 120]],
                  submoduleCfg                     : [],
                  userRemoteConfigs                : [[credentialsId: '00000000-0000-0000-0000-111111111111',
                                                       url          : "${GIT_URL}"]]])
      }
    }
    stage("同步源码") {
      steps {
        script {
          sh """bash -s << ENDSSH
ls -lh
git tag -n
git remote -v

git remote add zsj-upstream ${ZSJ_GIT_URL}
git config --global user.name devops-machine
git config --global user.email <EMAIL>
export http_proxy=http://************:60845
export https_proxy=http://************:60845
# 同步tag
git push zsj-upstream --tags
ENDSSH
"""
        }
      }
    }
  }
}
