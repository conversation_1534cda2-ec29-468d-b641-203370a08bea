package devops.maven

// 配置常量
class Constants {
  static final String GIT_CREDENTIALS_ID = '00000000-0000-0000-0000-111111111111'
  static final int GIT_TIMEOUT = 300
  static final int MAVEN_MEMORY = 2048
}

// TODO：发布的同时发布jakarta版本，如果不包含jakarta怎么办呢
// 允许不转换吗，已经是自己适配好的版本呢，参数是否转换
//  mvn help:all-profiles | grep "Profile Id: jakarta-transformer"
//  这个插件只能在Java 21上使用，所以必须选择Java 21

// 构建参数定义
def buildParameters() {
  return [
    [$class  : 'BuildDiscarderProperty',
     strategy: [$class: 'LogRotator', numToKeepStr: '500', artifactNumToKeepStr: '1000']
    ],
    parameters([
      string(name: 'git_url', defaultValue: '', description: '项目的Git地址，必须为http或https协议'),
      string(name: 'git_module', defaultValue: '', description: '项目的子模块，如果没有则保留为空'),
      string(name: 'git_tag', defaultValue: '', description: '项目分支或者tag名称'),
      choice(name: 'java_version', choices: ['8', '11', '17', '21', '24'], description: 'java版本'),
      choice(name: 'unit_test', choices: ['true', 'false'], description: '是否执行单元测试?'),
      choice(name: 'jakarta_transformer', choices: ['true', 'false'], defaultValue: 'true', description: '是否执行jakarta转换?'),
      string(name: 'maven_options', defaultValue: '', description: 'maven 命令附加参数'),
      string(name: 'crm_obj_id', defaultValue: '', description: '关联的crm对象id'),
      string(name: 'user_id', defaultValue: '', description: '关联的用户id'),
      string(name: 'remark', defaultValue: '', description: '备注信息')
    ])
  ]
}

// 验证构建参数
def validateParameters() {
  if (isNullOrBlank(params.git_url)) {
    error "git_url is empty"
  }
  if (isNullOrBlank(params.git_tag)) {
    error "git_tag is empty"
  }
  if (isNullOrBlank(params.java_version)) {
    error "java_version is empty"
  }
  if (isNullOrBlank(params.unit_test)) {
    error "unit_test is empty"
  }
}

// 打印构建信息
def printBuildInfo() {
  def buildInfo = [
    node  : NODE_NAME,
    params: params
  ]
  println groovy.json.JsonOutput.prettyPrint(groovy.json.JsonOutput.toJson(buildInfo))
}

// 准备Git目录
def prepareGitDirectory() {
  //base64要使用参数 -w， 避免结果换行
  String git_dir = sh(script: "echo ${params.git_url} | base64 -w 0", returnStdout: true).trim()
  echo "git_dir: ${git_dir}"
  return git_dir
}

// 重置Git仓库
def resetGitRepository() {
  if (fileExists('.git')) {
    try {
      sh(label: 'reset git', script: 'git reset --hard')
    } catch (e) {
      error("reset git error: " + e.message)
    }
  }
}

// 检出代码
def checkoutCode(String gitUrl) {
  try {
    checkout([$class                           : 'GitSCM',
              branches                         : [[name: "${params.git_tag}"]],
              doGenerateSubmoduleConfigurations: false,
              extensions                       : [[$class: 'PruneStaleBranch'],
                                                  [$class: 'CheckoutOption', timeout: Constants.GIT_TIMEOUT],
                                                  [$class: 'CloneOption', timeout: Constants.GIT_TIMEOUT]],
              submoduleCfg                     : [],
              userRemoteConfigs                : [[credentialsId: Constants.GIT_CREDENTIALS_ID, url: gitUrl]]])
  } catch (Exception e) {
    echo("git fetch failed, will delete dir and retry. error: " + e.message)
    deleteDir()
    checkout([$class                           : 'GitSCM',
              branches                         : [[name: "${params.git_tag}"]],
              doGenerateSubmoduleConfigurations: false,
              extensions                       : [[$class: 'PruneStaleBranch'],
                                                  [$class: 'CheckoutOption', timeout: Constants.GIT_TIMEOUT],
                                                  [$class: 'CloneOption', timeout: Constants.GIT_TIMEOUT]],
              submoduleCfg                     : [],
              userRemoteConfigs                : [[credentialsId: Constants.GIT_CREDENTIALS_ID, url: gitUrl]]])
  }
}

// 构建Maven命令
def buildMavenCommand() {
  def mvnCmd = "mvn -B -fae -am -e -U clean deploy -Dmaven.javadoc.skip --settings /usr/share/maven/ref/settings.xml"

  if (params.git_module) {
    mvnCmd += " -pl ${params.git_module}"
  }

  if (params.unit_test == 'false') {
    mvnCmd += " -Dmaven.test.skip"
  }

  if (params.maven_options) {
    mvnCmd += " ${params.maven_options}"
  }

  mvnCmd += " -Dmaven.wagon.http.ssl.insecure=true -Dserver -Dhttps.protocols=TLSv1.2 " +
    "-Dorg.slf4j.simpleLogger.showDateTime=true -Dorg.slf4j.simpleLogger.dateTimeFormat=HH:mm:ss.SSS"

  return mvnCmd
}

def getPackageName() {
  def packageName = "--"
  try {
    def mvnCmd = "mvn help:evaluate -Dexpression=project.build.finalName -q -DforceStdout"
    if (params.git_module) {
      mvnCmd += " -pl ${params.git_module}"
    }
    packageName = executeMavenBuild(mvnCmd, true) + ".jar"
  } catch (Exception e) {
    echo("get package name error: " + e.message)
  }
  return packageName
}

// 执行Maven构建
String executeMavenBuild(String mvnCmd, returnStdout = false) {
  def imageLabel = "openjdk${params.java_version}"
  def imagePath = "reg.foneshare.cn/base/fs-maven3.9:${imageLabel}"
  def result = "--"

  sh(label: 'image pull', script: "docker pull ${imagePath} || true")

  docker.image("${imagePath}").inside(
    "--privileged -v \$HOME/.m2:/root/.m2 --network=host " +
      "-e MAVEN_OPTS=-Xmx${Constants.MAVEN_MEMORY}m -e MAVEN_REPO_HOST=maven.foneshare.cn"
  ) {
    if (returnStdout) {
      result = sh(label: 'mvn deploy', script: mvnCmd, returnStdout: true).trim()
    } else {
      sh(label: 'mvn deploy', script: mvnCmd)
    }
  }
  return result
}

def sendBuildNotification(jobStatus, jobStatusMsg, packageName) {
  def content = []
  if (jobStatus) {
    content << "✅Jar包部署成功"
  } else {
    content << "❌ Jar包部署失败"
    content << "【错误信息】: ${jobStatusMsg}"
  }
  content << "【Jar名称】: ${packageName}"
  content << "【Git仓库】: ${params.git_url}"
  content << "【Maven模块】: ${params.git_module}"
  content << "【Git分支/Tag】: ${params.git_tag}"
  content << "【构建节点】: ${NODE_NAME}"
  content << "【构建用户】: https://www.fxiaoke.com/XV/UI/Home#profile/=/empid-${params.user_id}"
  content << "【Jenkins地址】: https://jenkins2.foneshare.cn/job/maven-deploy/${currentBuild.number}/console"

  def qinXinParam = [
    sessionType: "publish",
    userIds    : [params.user_id.toInteger()],
    content    : content.join("\n"),
  ]

  def jsonPayload = groovy.json.JsonOutput.toJson(qinXinParam)

  // 发送构建失败通知
  sh(script: """
      curl -X POST -H 'Content-Type: application/json' \
      'https://k8s-app.foneshare.cn/api/v1/notify/qixin' \
      -d '${jsonPayload}' 
    """, returnStatus: true)
}

// 主流程
properties(buildParameters())

def jobStatus = true
def jobStatusMsg = "SUCCESS"
def packageName = "--"

node('k8s-publish') {
  try {
    String git_dir = prepareGitDirectory()

    dir(git_dir) {
      stage("build info") {
        printBuildInfo()
        validateParameters()
      }

      stage("git fetch") {
        String gitUrl = params.git_url.replace('http://', 'https://')
        resetGitRepository()

        script {
          currentBuild.displayName = "${gitUrl}@${params.git_module}@${params.git_tag}"
        }

        checkoutCode(gitUrl)
      }

      stage('maven deploy') {
        def mvnCmd = buildMavenCommand()
        executeMavenBuild(mvnCmd, false)
      }

      stage('get package name') {
        packageName = getPackageName()
      }
    }
  } catch (Exception e) {
    jobStatus = false
    jobStatusMsg = e.message
    throw e
  } finally {
    sendBuildNotification(jobStatus, jobStatusMsg, packageName)
  }
}

// 工具方法
static def isNullOrBlank(String s) {
  return s == null || s.trim() == ''
}