// ================= 工具函数 =================
def echoWithLabel(String message, String label) {
  if (label) {
    sh(label: label, script: "echo -e \"$message\"")
  } else {
    echo message
  }
}

def isNullOrBlank(String s) {
  return s == null || s.trim() == ''
}


def buildDockerImage() {
  def baseImage = params.artifactBaseImage
  def gitUrl = params.gitUrl
  def module = params.gitModule
  def tag = params.gitTag
  def commitId = params.commitId
  def author = params.author.replace(" ", "") ?: "unknown"
  def buildRemark = params.artifactRemark ?: ''
  buildRemark = buildRemark.replace(' ', '')
  // 由于通过Harbor API无法获取容器Label,同时维护者信息业务上基本不用，因此把备注信息写入MAINTAINER中
  def content = "FROM ${baseImage}\n" +
    "LABEL MAINTAINER=${author}\n" +
    "LABEL gitUrl=${gitUrl}\n" +
    "LABEL gitModule=${module}\n" +
    "LABEL gitTag=${tag}\n" +
    "LABEL commitId=${commitId}\n" +
    "LABEL buildRemark=${buildRemark}\n" +
    "WORKDIR /opt/fs-app\n" +
    "COPY requirements.txt .\n" +
    "RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && pip config set install.trusted-host mirrors.aliyun.com \n" +
    "RUN pip install --no-cache-dir -r requirements.txt\n" +
    "COPY src/ ./src/"

  // 写入本地的Dockerfile
  sh(label: 'delete old Dockerfile', script: 'rm -f Dockerfile')
  echoWithLabel(content, "Dockerfile")
  writeFile file: 'Dockerfile', text: content, encoding: 'UTF-8'

  sh(label: 'docker build', script: "docker build --quiet -t ${params.artifactImage} .")
  sh(label: 'docker push', script: "docker push ${params.artifactImage}")
}

// ================= 主流程 pipeline =================
properties([
  [$class: 'BuildDiscarderProperty', strategy: [$class: 'LogRotator', numToKeepStr: '5000', artifactNumToKeepStr: '5000']],
  parameters([
    string(name: 'gitUrl', defaultValue: '', description: 'Git地址'),
    string(name: 'gitModule', defaultValue: '', description: 'Git子模块'),
    string(name: 'gitTag', defaultValue: '', description: 'Git分支或Tag'),
    string(name: 'commitId', defaultValue: '', description: 'Git提交ID'),
    string(name: 'artifactBaseImage', defaultValue: '', description: '基础镜像'),
    string(name: 'artifactImage', defaultValue: '', description: '目标镜像'),
    string(name: 'artifactRemark', defaultValue: '', description: '镜像备注'),
    choice(name: 'unitTest', choices: ['no', 'yes'], description: '是否执行单元测试?'),
    string(name: 'mavenOptions', defaultValue: '', description: 'maven 命令附加参数'),
    choice(name: 'forceBuild', choices: ['no', 'yes'], description: '是否强制构建？'),
    choice(name: 'dependencyCheck', choices: ['yes', 'no'], description: '是否进行依赖包检测？'),
    string(name: 'parentPom', defaultValue: '', description: '应用父POM'),
    choice(name: 'package', choices: ['war', 'jar'], description: '打包类型'),
    string(name: 'mavenImage', defaultValue: 'reg.firstshare.cn/base/fs-maven3.9:openjdk8', description: 'maven镜像'),
    string(name: 'author', defaultValue: 'unknown', description: '操作人'),
    string(name: 'uuid', defaultValue: '--', description: 'uuid')])
])

node('k8s-publish') {
  def workDir = params.gitUrl.replace("https://git.firstshare.cn/", "").replace(".git", "")
  dir(workDir) {
    stage("参数解析") {

      script {
        pwd()
        if (params.gitUrl.contains("http://")) {
          error "git 仓库地址必须使用https协议，不支持http协议"
        }
        println "node: ${NODE_NAME}"
        println "params: " + groovy.json.JsonOutput.prettyPrint(groovy.json.JsonOutput.toJson(params))
        if (isNullOrBlank(params.parentPom)) {
          error "parent pom is blank"
        }
      }
    }
    stage("拉取代码") {
      script {
        checkoutCode()
      }
    }
    stage("镜像检查") {
      script {
        catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: '镜像检查失败') {
          def exist = checkImageIsExist()
          if (exist) {
            echo "image exist, skip build: ${params.artifactImage}"
            return
          }
        }
      }
    }
    stage("镜像构建") {
      script {
        imageBuild()
      }
    }
  }
}

// ================= stage函数实现 =================
def checkoutCode() {
  // 重置git避免更新失败
  if (fileExists('.git')) {
    try {
      sh(label: 'reset git', script: 'git reset --hard')
    } catch (e) {
      echo "reset git error: " + e.message
      deleteDir()
    }
  }
  try {
    sh """git config --global http.sslVerify false"""
    checkout([$class                           : 'GitSCM',
              branches                         : [[name: "${params.commitId}"]],
              doGenerateSubmoduleConfigurations: false,
              extensions                       : [[$class: 'PruneStaleBranch'],
                                                  [$class: 'CheckoutOption', timeout: 30],
                                                  [$class: 'CloneOption', timeout: 30]],
              submoduleCfg                     : [],
              userRemoteConfigs                : [[credentialsId: '00000000-0000-0000-0000-111111111111', url: params.gitUrl]]])
  } catch (e) {
    // 清理工作空间，便于下次任务正常执行
    deleteDir()
    error "代码拉取失败，已对工作空间进行了清理, 请进行重试操作。 err:  " + e.message
  }
}

def checkImageIsExist() {
  // 强制构建，删除本地的镜像
  if (params.forceBuild == 'yes') {
    println "开启了强制构建，跳过镜像检查"
    return false
  } else {
    // 尝试拉取docker镜像，如果失败则继续构建
    sh(label: 'image pull', script: "docker pull ${params.artifactImage} || true")
    // 根据commitId确定是否已经存在了
    def cmd = "docker inspect --format='{{index .Config.Labels \"commitId\"}}' ${params.artifact_image} || true"
    return sh(label: 'image query', script: cmd, returnStdout: true).trim().contains(params.commitId)
  }
}


def imageBuild() {
  buildDockerImage()
}
