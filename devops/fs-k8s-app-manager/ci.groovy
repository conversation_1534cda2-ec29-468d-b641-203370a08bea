// ================= 工具函数 =================
def echoWithLabel(String message, String label) {
  if (label) {
    sh(label: label, script: "echo -e \"$message\"")
  } else {
    echo message
  }
}

def isNullOrBlank(String s) {
  return s == null || s.trim() == ''
}

def firstNotBlank(String... args) {
  for (String s : args) {
    if (!isNullOrBlank(s)) {
      return s
    }
  }
  return null
}

def extractVersion(String ver) {
  int pos = ver.indexOf('-')
  if (pos > 0) {
    ver = ver.substring(0, pos)
  }
  return ver
}

def versionComparator(String a, String b) {
  def o1 = extractVersion(a).tokenize('.')
  def o2 = extractVersion(b).tokenize('.')
  def len = Math.min(o1.size(), o2.size())
  int ret = 0
  for (int i = 0; i < len; i++) {
    def v1 = o1[i] as int
    def v2 = o2[i] as int
    ret = v1 - v2
    if (ret != 0) {
      break
    }
  }
  return ret
}

def isIgnoreJava21Check(String gitModule) {
  String[] ignoreJava21CheckGitModules = [
    "egress-api-service"
  ]
  return ignoreJava21CheckGitModules.any { regex -> gitModule ==~ regex }
}

def multiArchBuildEnabled(String gitModule) {
  String[] ignoreGitModules = ["empty"]
  return !ignoreGitModules.any { regex -> gitModule ==~ regex }
}

// ================= 业务逻辑函数 =================
def jarVersionNecessaryConf(String parentPom) {
  def ret = [
    'notifier-support'                  : '5.6.0',
    'fs-paas-auth-api'                  : '1.0.0-SNAPSHOT',
    'fs-paas-auth-client'               : '2.0.0-SNAPSHOT',
    'biz-log-client'                    : '1.1.0-SNAPSHOT',
    'biz-log-proto'                     : '1.1.0-SNAPSHOT',
    'rpc-trace'                         : '4.0.0-SNAPSHOT',
    'spring-support'                    : '3.0.0-SNAPSHOT',
    'mongo-spring-support'              : '3.0.0-SNAPSHOT',
    'mybatis-spring-support'            : '5.2.0',
    'jdbc-support'                      : '4.1.1-SNAPSHOT',
    'jedis-spring-support'              : '4.2.0-SNAPSHOT',
    'config-core'                       : '7.5.0-SNAPSHOT',
    'dubbo'                             : '2.9.4-SNAPSHOT',
    'zkclient'                          : '0.11',
    'core-filter'                       : '3.2.0-SNAPSHOT',
    'dubbo-support'                     : '1.1.2',
    'metrics-oss'                       : '6.0.0-SNAPSHOT',
    'fastjson'                          : '1.2.83',
    'jedis'                             : '3.7.1',
    'commons-pool2'                     : '2.10.0',
    'logconfig-core'                    : '3.0.0-SNAPSHOT',
    'jutil'                             : '1.1.0-SNAPSHOT',
    'netty-all'                         : '4.1.74.Final',
    'netty-common'                      : '4.1.74.Final',
    'netty-buffer'                      : '4.1.74.Final',
    'netty-codec'                       : '4.1.74.Final',
    'netty-transport'                   : '4.1.74.Final',
    'i18n-client'                       : '3.2.8-SNAPSHOT',
    'http-spring-support'               : '3.4.0-SNAPSHOT',
    'caffeine'                          : '2.9.3',
    'ehcache'                           : '3.8.1',
    'fs-paas-license-api'               : '1.1.0-SNAPSHOT',
    'validation-api'                    : '2.0.0.Final',
    'fs-restful-common'                 : '1.0.2-SNAPSHOT',
    'fs-restful-server'                 : '1.0.2-SNAPSHOT',
    'fs-restful-client'                 : '1.0.2-SNAPSHOT',
    'fs-resteasy-support'               : '1.0.2-SNAPSHOT',
    'fs-metadata-provider'              : '9.6.0-SNAPSHOT',
    'fs-common-gray-release'            : '2.2.0-SNAPSHOT',
    'gray-release'                      : '1.0.4',
    'postgresql'                        : '42.3.5',
    'fs-fsi-proxy'                      : '3.0.0-SNAPSHOT',
    'fs-uc-api'                         : '1.0.3-SNAPSHOT',
    'fs-enterprise-id-account-converter': '1.1-SNAPSHOT',
    'xstream'                           : '1.4.20',
  ]
  if (parentPom.endsWith("-alpha")) {
    ret['fs-paas-auth-api'] = '2.4.0-SNAPSHOT'
    ret['fs-paas-auth-client'] = '2.4.0-SNAPSHOT'
    ret['okhttp'] = '3.14.9'
    ret['fs-social-api'] = '1.0.2-SNAPSHOT'
    ret['rocketmq-client'] = '5.3.0'
    ret['fs-rocketmq-support'] = '5.3.0-SNAPSHOT'
    ret['fs-common-mq'] = '5.3.0-SNAPSHOT'
    ret['fs-uc-api'] = '1.1.2-SNAPSHOT'
    ret['fs-sandbox-api'] = '2.0.4-SNAPSHOT'
    ret['fs-cache'] = '2.0.1-SNAPSHOT'
    ret['fs-change-set-api'] = '2.0.4-SNAPSHOT'
  } else if (parentPom.endsWith("-forceecrm-rc")) {
    ret['mq-dispatcher'] = '5.0.2-SNAPSHOT'
    ret['rocketmq-client'] = '5.3.0'
    ret['fs-common-mq'] = '5.3.0-SNAPSHOT'
    ret['fs-rocketmq-support'] = '5.3.0-SNAPSHOT'
    ret['fs-uc-api'] = '1.1.2-SNAPSHOT'
    ret['fs-sandbox-api'] = '2.0.4-SNAPSHOT'
    ret['fs-paas-app-flow'] = '9.3.0-SNAPSHOT'
    ret['fs-cache'] = '2.0.1-SNAPSHOT'
    ret['fs-change-set-api'] = '2.0.3-rocketmq-SNAPSHOT'
  } else if (parentPom.endsWith("-rc")) {
    ret['rocketmq-client'] = '5.3.0'
    ret['fs-rocketmq-support'] = '5.3.0-SNAPSHOT'
    ret['fs-common-mq'] = '5.3.0-SNAPSHOT'
    ret['fs-uc-api'] = '1.1.2-SNAPSHOT'
    ret['fs-sandbox-api'] = '2.0.4-SNAPSHOT'
    ret['fs-cache'] = '2.0.1-SNAPSHOT'
    ret['fs-change-set-api'] = '2.0.4-SNAPSHOT'
  }
  def regex = ~/^\d+(\d|\.)+\d+/
  ret.each { key, value ->
    def matcher = value =~ regex
    if (matcher.find()) {
      ret.put(key, matcher.group())
    } else {
      println "missMatch, key: ${key}, val: $value"
    }
  }
  return ret
}

def scanSpringBootJarVersion(String glob) {
  String jars = sh(script: "jar -tf ${glob} |  grep BOOT-INF/lib/ | grep jar || true", returnStdout: true)
  def jarNames = jars.split('\n').collect { it.trim() }
  Map<String, List<String>> jarVersion = [:]
  def regex = ~/-(\d+).(\d+)/
  jarNames.each { jarName ->
    String name = jarName.replace("BOOT-INF/lib/", "")
    def matcher = name =~ regex
    if (matcher.find()) {
      def key = name.substring(0, matcher.start())
      def ver = name.substring(matcher.start(1))
      def versions = jarVersion.get(key)
      if (versions == null) {
        versions = []
        jarVersion.put(key, versions)
      }
      versions << ver
    } else {
      println "missMatch, name: ${name}"
    }
  }
  return jarVersion

}

def scanJarVersion(String glob) {
  Map<String, List<String>> jarVersion = [:]
  println("find jar files from: ${glob}")
  def files = findFiles(glob: glob)
  // 寻找中横线后至少两位数字的版本号位置
  // commons-beanutils-1.9.4.jar
  // commons-lang3-3.12.0.jar
  // spring-plugin-metadata-1.2.0.RELEASE.jar
  // fs-metadata-api-8.6.0-SNAPSHOT.jar

  println "============  jar ================"
  def jarNames = files.collect { it.getName() }
  println(jarNames.sort().join('\n'))
  println "============  jar ================"

  def regex = ~/-(\d+).(\d+)/
  files.each { file ->
    String name = file.getName()
    def matcher = name =~ regex
    if (matcher.find()) {
      def key = name.substring(0, matcher.start())
      def ver = name.substring(matcher.start(1))
      def versions = jarVersion.get(key)
      if (versions == null) {
        versions = []
        jarVersion.put(key, versions)
      }
      versions << ver
    } else {
      println "missMatch, name: ${name}"
    }
  }
  return jarVersion
}

def checkJarVersions(jars, minVersions) {
  List<String> conflicts = []
  minVersions.each { k, v ->
    def kinds = jars[k]
    def min = minVersions[k]
    if (min && kinds) {
      kinds.each { now ->
        if (versionComparator(min, now) > 0) {
          echoWithLabel("jar包版本过低: ${k}-${now} < ${min}，(建议从公司父pom继承版本号，不要自己指定)", '最低版本限制')
          conflicts.add("${k}-${now}")
        }
      }
    }
  }
  if (conflicts) {
    error "jar包版本过低: ${conflicts}"
  }
}

def checkJarConflicts(jars) {
  // 检查所有同名的jar包
  conflicts = []
  // 有些jar包名一样，但是内部的类包名不一样，实际运行不冲突，这里先忽略
  Set<String> ignores = ['cglib', 'annotations', 'metrics-core', 'rocketmq-client', 'rocketmq-common', 'rocketmq-remoting', 'protostuff-api']
  jars.each { k, v ->
    if (ignores.contains(k)) {
      return
    }
    if (k.startsWith('netty') && k.contains('native')) {
      return
    }
    if (v.size() > 1) {
      conflicts.add(k)
    }
  }
  if (conflicts) {
    conflicts.each {
      def ver = jars[it]
      echoWithLabel("存在同名包版本冲突: ${it}: ${ver}", 'jar conflicts')
    }
    error "存在同名包版本冲突: ${conflicts}"
  }
}

def checkAnnotationApiConflicts(jars) {
  // 检查所有同名的jar包
  conflicts = []
  jars.each { k, v ->
    if (k.startsWith('jakarta.annotation-api') || k.startsWith('javax.annotation-api') || k.startsWith('jboss-annotations-api_1')) {
      conflicts.add(k)
    }
  }
  if (conflicts && conflicts.size() > 1) {
    conflicts.each {
      def ver = jars[it]
      echoWithLabel("存在多个 annotation-api 版本: ${it}: ${ver}", 'annotation-api version check')
    }
    error "存在多个 annotation-api 版本，可能导致annotation注解运行时异常: ${conflicts}"
  }
}

def checkSpecialCases(jars) {
  // 检查特定druid版本的一致性
  def druidVersion = jars['druid']?.first()
  def mybatisVersion = jars['mybatis-spring-support']?.first()
  def sql2esVersion = jars['fs-sql2esdsl']?.first()
  println "druidVersion: ${druidVersion}, mybatisVersion: ${mybatisVersion}, sql2esVersion: ${sql2esVersion}"

//  catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: 'rocketmq版本不一致') {
//    def mqSupportVersion = jars['fs-rocketmq-support']?.first()
//    if (mqSupportVersion?.startsWith('3.')) {
//      def mqClientVersion = jars['rocketmq-client']?.first()
//      def mqCommonVersion = jars['rocketmq-common']?.first()
//      def mqRemotingVersion = jars['rocketmq-remoting']?.first()
//      println "mqSupportVersion: ${mqSupportVersion}, mqClientVersion: ${mqClientVersion}, mqCommonVersion: ${mqCommonVersion}, mqRemotingVersion: ${mqRemotingVersion}"
//      if (!mqClientVersion?.startsWith('5.') && !mqCommonVersion?.startsWith('5.') && !mqRemotingVersion?.startsWith('5.')) {
//        error "rocketmq版本不一致: mqSupportVersion: ${mqSupportVersion}, mqClientVersion: ${mqClientVersion}, mqCommonVersion: ${mqCommonVersion}, mqRemotingVersion: ${mqRemotingVersion}, 请升级rocketmq版本到5.1.3，不要自己指定版本号，使用父 pom 指定版本"
//      }
//    }
//  }

  // 检查特定fs-metadata-provider版本的一致性
//  def fs_metadata_provider_ver = jars['fs-metadata-provider']?.first()
//  if (fs_metadata_provider_ver?.startsWith("9.4.0-SNAPSHOT")) {
//    def fs_stone_commons_client_ver = jars['fs-stone-commons-client']?.first()
//    def stoneCommonClientV = 0
//    if (fs_stone_commons_client_ver) {
//      stoneCommonClientV = fs_stone_commons_client_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger()
//    }
//    if (stoneCommonClientV < 165) {
//      error "当 fs-metadata-provider 的版本号为 9.4.0-SNAPSHOT 时，fs-stone-commons-client 的版本需大于等于 1.6.5。详情咨询 @钱凌锋 @安宜龙 \n" +
//        "fs-metadata-provider 当前版本：${fs_metadata_provider_ver}, fs-stone-commons-client 当前版本: ${fs_stone_commons_client_ver}"
//    }
//  }

  //需求来源 @李晨 , 时间：2025-07-16
  def fs_metadata_provider_ver = jars['fs-metadata-provider']?.first()
  if (fs_metadata_provider_ver) {
    def fs_rest_es7_support_ver = jars['fs-rest-es7-support']?.first()
    if (fs_rest_es7_support_ver) {
      def v = fs_rest_es7_support_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger()
      if (v < 360) {
        error "当依赖中包含 fs-metadata-provider 包时，fs-rest-es7-support 的版本必须大于等于 3.6.0，详情请咨询 @李晨。 \n" +
          "fs-metadata-provider 版本：${fs_metadata_provider_ver}, fs-rest-es7-support 版本: ${fs_rest_es7_support_ver}"
      }
    }
  }

  //需求来源 @周伟荣 , 时间：2025-08-11
  def fs_paas_app_metadata_ver = jars['fs-paas-app-metadata']?.first()
  if (fs_paas_app_metadata_ver) {
    if(fs_paas_app_metadata_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger() >= 970) {
      def fs_paas_expression_ver = jars['fs-paas-expression']?.first()
      if (fs_paas_expression_ver) {
        if (fs_paas_expression_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger() < 890) {
          error "如果 fs-paas-app-metadata ≥9.7.0，那么 fs-paas-expression 版本必须大于等于 8.9.0，详情请咨询 @周伟荣。 \n" +
            "fs-paas-app-metadata 版本：${fs_paas_app_metadata_ver}, fs-paas-expression 版本: ${fs_paas_expression_ver}"
        }
        if (params.gitUrl.contains("paas/fs-paas-function-engine.git")) {
            echo "函数服务，跳过groovy包版本校验"
        }else {
          def groovy_all_ver = jars['groovy-all']?.first()
          def groovy_ver = jars['groovy']?.first()
          if(groovy_all_ver) {
            if(groovy_all_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger() < 4023) {
              error "如果 fs-paas-app-metadata ≥9.7.0，那么 groovy-all 版本必须大于等于 4.0.23，详情请咨询 @周伟荣。 \n" +
                "fs-paas-app-metadata 版本：${fs_paas_app_metadata_ver}, groovy-all 版本: ${groovy_all_ver}"
            }
          }
          if(groovy_ver) {
            if(groovy_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger() < 4023) {
              error "如果 fs-paas-app-metadata ≥9.7.0，那么 groovy 版本必须大于等于 4.0.23，详情请咨询 @周伟荣。 \n" +
                "fs-paas-app-metadata 版本：${fs_paas_app_metadata_ver}, groovy 版本: ${groovy_ver}"
            }
          }
          if(groovy_all_ver && groovy_ver && groovy_all_ver != groovy_ver) {
            error "如果 fs-paas-app-metadata ≥9.7.0，那么 groovy-all 版本必须与 groovy 版本一致，详情请咨询 @周伟荣。 \n" +
              "fs-paas-app-metadata 版本：${fs_paas_app_metadata_ver}, groovy-all 版本: ${groovy_all_ver}, groovy 版本: ${groovy_ver}"
          }
        }
      }
    }
  }

  //需求来源 @赵琚 , 时间：2025-07-31
  def fs_paas_app_common_ver = jars['fs-paas-app-common']?.first()
  if (fs_paas_app_common_ver) {
    def fs_enterpriserelation_rest_api2_ver = jars['fs-enterpriserelation-rest-api2']?.first()
    if (fs_enterpriserelation_rest_api2_ver) {
      def v1 = fs_paas_app_common_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger()
      def v2 = fs_enterpriserelation_rest_api2_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger()
      if (v1 >= 965 && v2 < 218) {
        error "fs-paas-app-common-9.6.5 依赖的fs-enterpriserelation-rest-api2版本必须大于等于 2.1.8，详情请咨询 @赵琚。 \n" +
          "fs-paas-app-common 版本：${fs_paas_app_common_ver}, fs-enterpriserelation-rest-api2 版本: ${fs_enterpriserelation_rest_api2_ver}"
      }
    }
  }

  catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: '请注意，这里存在Jar包版本冲突，请注意检查') {
    // 检查cglib-nodep不可与cglib、asm共存
    def cglibNodep = jars['cglib-nodep']?.first()
    if (cglibNodep) {
      def cglib = jars['cglib']?.first()
      def asm = jars['asm']?.first()
      if (cglib || asm) {
        error "cglib-nodep不可与cglib、asm共存，若要使用cglib-nodep，则需要排除掉所有的cglib和asm，这是因为cglib-nodep = cglib + asm。" +
          "参考资料：https://sunyinjie.github.io/2018/04/01/Cglib%E5%BC%95%E5%8F%91%E7%9A%84%E8%A1%80%E6%A1%88%E2%80%94%E2%80%94Jar%E5%8C%85%E4%BE%9D%E8%B5%96%E5%86%B2%E7%AA%81%E6%80%BB%E7%BB%93/"
      }
    }
  }

  // 检查rocketmq版本一致性
  def rocketmqClientVer = jars['rocketmq-client']?.first()
  def rocketmqCommonVer = jars['rocketmq-common']?.first()
  def rocketmqAclVer = jars['rocketmq-acl']?.first()
  def rocketmqRemotingVer = jars['rocketmq-remoting']?.first()
  if (rocketmqClientVer?.startsWith("5.3.0")) {
    println "rocketmq-client: ${rocketmqClientVer}, rocketmq-common: ${rocketmqCommonVer}, rocketmq-acl: ${rocketmqAclVer}, rocketmq-remoting: ${rocketmqRemotingVer}"
    if (rocketmqCommonVer && rocketmqCommonVer != rocketmqClientVer) {
      error "rocketmq-common 版本号 必须要跟 rocketmq-client 版本一致。详情请联系 @谷广田"
    }
    if (rocketmqAclVer && rocketmqAclVer != rocketmqClientVer) {
      error "rocketmq-acl 版本号 必须要跟 rocketmq-client 版本一致。详情请联系 @谷广田"
    }
    if (rocketmqRemotingVer && rocketmqRemotingVer != rocketmqClientVer) {
      error "rocketmq-remoting 版本号 必须要跟 rocketmq-client 版本一致。详情请联系 @谷广田"
    }
  }
}

def buildDockerImage(pkgPath, pkg) {
  def baseImage = params.artifactBaseImage
  def gitUrl = params.gitUrl
  def module = params.gitModule
  def tag = params.gitTag
  def commitId = params.commitId
  def author = params.author.replace(" ", "") ?: "unknown"
  def buildRemark = params.artifactRemark ?: ''
  buildRemark = buildRemark.replace(' ', '')
  // 由于通过Harbor API无法获取容器Label,同时维护者信息业务上基本不用，因此把备注信息写入MAINTAINER中
  def content = "FROM ${baseImage}\n" +
    "LABEL MAINTAINER=${author}\n" +
    "LABEL gitUrl=${gitUrl}\n" +
    "LABEL gitModule=${module}\n" +
    "LABEL gitTag=${tag}\n" +
    "LABEL commitId=${commitId}\n" +
    "LABEL buildRemark=${buildRemark}\n" +
    "ENV ARTIFACT_TYPE=${pkg}\n" +
    "ADD ${pkgPath} /fs-artifact/fs.${pkg}\n"

  // 写入本地的Dockerfile
  sh(label: 'delete old Dockerfile', script: 'rm -f Dockerfile')
  echoWithLabel(content, "Dockerfile")
  writeFile file: 'Dockerfile', text: content, encoding: 'UTF-8'
  //线上环境还没有启用buildx，等线上也启动并稳定后，留下面两行即可
  String hostHostname = sh(script: "hostname", returnStdout: true).trim()
  def multiArchEnabled = hostHostname.contains("firstshare")
  if (multiArchEnabled && multiArchBuildEnabled(module)) {
    def platform = multiArchEnabled ? "linux/amd64,linux/arm64" : "linux/amd64"
    sh(label: 'docker build push', script: "docker buildx build --push --platform ${platform} --provenance=false --output=oci-mediatypes=false --tag ${params.artifactImage} .")
  } else {
    sh(label: 'docker build', script: "docker build --quiet -t ${params.artifactImage} .")
    sh(label: 'docker push', script: "docker push ${params.artifactImage}")
  }
}

// ================= 主流程 pipeline =================
properties([
  [$class: 'BuildDiscarderProperty', strategy: [$class: 'LogRotator', numToKeepStr: '5000', artifactNumToKeepStr: '5000']],
  parameters([
    string(name: 'gitUrl', defaultValue: '', description: 'Git地址'),
    string(name: 'gitModule', defaultValue: '', description: 'Git子模块'),
    string(name: 'gitTag', defaultValue: '', description: 'Git分支或Tag'),
    string(name: 'commitId', defaultValue: '', description: 'Git提交ID'),
    string(name: 'artifactBaseImage', defaultValue: '', description: '基础镜像'),
    string(name: 'artifactImage', defaultValue: '', description: '目标镜像'),
    string(name: 'artifactRemark', defaultValue: '', description: '镜像备注'),
    choice(name: 'unitTest', choices: ['no', 'yes'], description: '是否执行单元测试?'),
    string(name: 'mavenOptions', defaultValue: '', description: 'maven 命令附加参数'),
    choice(name: 'forceBuild', choices: ['no', 'yes'], description: '是否强制构建？'),
    choice(name: 'dependencyCheck', choices: ['yes', 'no'], description: '是否进行依赖包检测？'),
    string(name: 'parentPom', defaultValue: '', description: '应用父POM'),
    choice(name: 'package', choices: ['war', 'jar'], description: '打包类型'),
    string(name: 'mavenImage', defaultValue: 'reg.firstshare.cn/base/fs-maven3.9:openjdk8', description: 'maven镜像'),
    string(name: 'author', defaultValue: 'unknown', description: '操作人'),
    string(name: 'uuid', defaultValue: '--', description: 'uuid')])
])

node('k8s-publish') {
  def workDir = params.gitUrl.replace("https://git.firstshare.cn/", "").replace(".git", "")
  dir(workDir) {
    stage("参数解析") {

      script {
        pwd()
        if (params.gitUrl.contains("http://")) {
          error "git 仓库地址必须使用https协议，不支持http协议"
        }
        println "node: ${NODE_NAME}"
        println "params: " + groovy.json.JsonOutput.prettyPrint(groovy.json.JsonOutput.toJson(params))
        if (isNullOrBlank(params.parentPom)) {
          error "parent pom is blank"
        }
      }
    }
    stage("拉取代码") {
      script {
        checkoutCode()
      }
    }
    stage("镜像检查") {
      script {
        catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: '镜像检查失败') {
          def exist = checkImageIsExist()
          if (exist) {
            echo "image exist, skip build: ${params.artifactImage}"
            return
          }
        }
      }
    }
    stage("修改父pom") {
      script {
        modifyPom()
      }
    }
    stage("Maven编译") {
      script {
        mavenBuild()
      }
    }
    stage("java21兼容性检查") {
      script {
        echo "skip java21 check"
        //java21Check()
      }
    }
    stage("Jar包校验") {
      script {
        if (params.dependencyCheck == 'yes') {
          dependenciesCheck()
        }
      }
    }
    stage("镜像构建") {
      script {
        imageBuild()
      }
    }
  }
}

// ================= stage函数实现 =================
def checkoutCode() {
  // 重置git避免更新失败
  if (fileExists('.git')) {
    try {
      sh(label: 'reset git', script: 'git reset --hard')
    } catch (e) {
      echo "reset git error: " + e.message
      deleteDir()
    }
  }
  try {
    sh """git config --global http.sslVerify false"""
    checkout([$class                           : 'GitSCM',
              branches                         : [[name: "${params.commitId}"]],
              doGenerateSubmoduleConfigurations: false,
              extensions                       : [[$class: 'PruneStaleBranch'],
                                                  [$class: 'CheckoutOption', timeout: 30],
                                                  [$class: 'CloneOption', timeout: 30]],
              submoduleCfg                     : [],
              userRemoteConfigs                : [[credentialsId: '00000000-0000-0000-0000-111111111111', url: params.gitUrl]]])
  } catch (e) {
    // 清理工作空间，便于下次任务正常执行
    deleteDir()
    error "代码拉取失败，已对工作空间进行了清理, 请进行重试操作。 err:  " + e.message
  }
}

def checkImageIsExist() {
  // 强制构建，删除本地的镜像
  if (params.forceBuild == 'yes') {
    println "开启了强制构建，跳过镜像检查"
    return false
  } else {
    // 尝试拉取docker镜像，如果失败则继续构建
    sh(label: 'image pull', script: "docker pull ${params.artifactImage} || true")
    // 根据commitId确定是否已经存在了
    def cmd = "docker inspect --format='{{index .Config.Labels \"commitId\"}}' ${params.artifact_image} || true"
    return sh(label: 'image query', script: cmd, returnStdout: true).trim().contains(params.commitId)
  }
}

def modifyPom() {
  // 2. 修改父pom
  String[] poms = sh(script: "find . -name pom.xml", returnStdout: true).trim().split('\n')
  if (params.parentPom != "fxiaoke-parent-pom") {
    def parentPomSuffix = params.parentPom.replace("fxiaoke-parent-pom", "")
    def localParentPoms = ["fxiaoke-parent-pom", "fxiaoke-spring-cloud-parent"]
    localParentPoms.each { localParentPom ->
      def newParentPom = "${localParentPom}${parentPomSuffix}"
      echo "=== 尝试替换父pom: ${localParentPom} 为 ${newParentPom} ==="
      poms.each { pom ->
        sh(label: "修改父pom: ${pom}", script: "sed -i 's@<artifactId>${localParentPom}</artifactId>@<artifactId>${newParentPom}</artifactId>@g' ${pom}", returnStatus: true)
        sh(label: "查看父pom: ${pom}", script: "grep ${localParentPom} -C 3 ${pom} || true")
      }
    }
  }
}

def mavenBuild() {
  // 3. 编译
  def mvnCmd = 'mvn -B -fae -ntp -am -e -U clean package -Dmaven.javadoc.skip -Dsun.jnu.encoding=UTF-8 -Dfile.encoding=UTF-8 '
  if (params.gitModule) {
    mvnCmd += ' -pl ' + params.gitModule
  }
  if (params.unitTest == 'no') {
    mvnCmd += ' -Dmaven.test.skip'
  }
  if (params.mavenOptions) {
    mvnCmd += ' ' + params.mavenOptions
  }
  mvnCmd += ' -Dmaven.wagon.http.ssl.insecure=true -Dserver -Dhttps.protocols=TLSv1.2 -Djava.awt.headless=true -Dorg.slf4j.simpleLogger.showDateTime=true -Dorg.slf4j.simpleLogger.dateTimeFormat=HH:mm:ss.SSS'
  String hostHostname = sh(script: "hostname", returnStdout: true).trim()
  String mavenRepoHost = hostHostname.contains("firstshare") ? "maven.firstshare.cn" : "maven.foneshare.cn"
  docker.image("${params.mavenImage}").inside('--privileged -v $HOME/.m2:/root/.m2 --network=host -e LANG=en_US.UTF-8 -e LC_ALL=en_US.UTF-8 -e MAVEN_OPTS=-Xmx4096m ' + "-e MAVEN_REPO_HOST=${mavenRepoHost} -h maven.${hostHostname}") {
    sh(label: 'mvn building', script: mvnCmd)
  }
}

// java21 check，辅助升级Java21版本
def java21Check() {
  catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: 'Java 21 API兼容性报告运行错误') {
    String hostHostname = sh(script: "hostname", returnStdout: true).trim()
    // only for firstshare
    if (!hostHostname.contains("firstshare")) {
      echoWithLabel("skip java check for foneshare", 'java21 check')
      return
    }
    // params.mavenImage是Java21以上
    if (!params.mavenImage.contains("openjdk2")) {
      echoWithLabel("skip java check, only support java21+", 'java21 check')
      return
    }
    if (isIgnoreJava21Check(params.gitModule)) {
      echoWithLabel("skip java check, ignore module", 'java21 check')
      return
    }
    String mavenRepoHost = hostHostname.contains("firstshare") ? "maven.firstshare.cn" : "maven.foneshare.cn"
    def mvnCmd = 'mvn -B -fae -ntp -am -e -U process-classes org.eclipse.emt4j:emt4j-maven-plugin:0.91:process -DfromVersion=8 -DtoVersion=21 -DoutputFile=target/emt4j-report.html '
    if (params.gitModule) {
      mvnCmd += ' -pl ' + params.gitModule
    }
    if (params.mavenOptions) {
      mvnCmd += ' ' + params.mavenOptions
    }
    mvnCmd += ' -Dmaven.wagon.http.ssl.insecure=true -Dserver -Dhttps.protocols=TLSv1.2 -Djava.awt.headless=true -Dorg.slf4j.simpleLogger.showDateTime=true -Dorg.slf4j.simpleLogger.dateTimeFormat=HH:mm:ss.SSS'

    docker.image("${params.mavenImage}").inside('--privileged -v $HOME/.m2:/root/.m2 --network=host -e MAVEN_OPTS=-Xmx2048m ' + "-e MAVEN_REPO_HOST=${mavenRepoHost} -h maven.${hostHostname}") {
      sh(label: 'mvn building', script: mvnCmd)
    }
    // 发布成HTML报告
    publishHTML(target: [allowMissing         : true,
                         alwaysLinkToLastBuild: false,
                         keepAll              : true,
                         reportDir            : 'target',
                         reportFiles          : 'emt4j-report.html',
                         reportName           : 'java-emt4j-report',
                         reportTitles         : params.gitModule + ' Java 21 API兼容性报告'])

    echo "请点击URL查看${params.gitModule}的Java 21 API兼容性报告: ${env.BUILD_URL}java-emt4j-report"
    sh(label: 'delete emt4j cache', script: 'rm -rf .emt4j')
  }
}

def dependenciesCheck() {
  // 4. 依赖包版本校验
  def basePath = params.gitModule ? params.gitModule + "/" : ''
  def jars = scanJarVersion("${basePath}target/*/WEB-INF/lib/*.jar")
  def size = jars.size()
  // spring boot 项目单独扫描
  if (size == 0) {
    jars = scanSpringBootJarVersion("${basePath}target/*.jar")
    size = jars.size()
  }
  echoWithLabel("found ${size} jars", 'scan jars')

  // 检查jar包的最低版本号要求
  def minVersions = jarVersionNecessaryConf(params.parentPom)
  checkJarVersions(jars, minVersions)

  checkSpecialCases(jars)
}

def imageBuild() {
  // 5. 构建docker镜像
  def basePath = params.gitModule ?: '.'
  def pkg = ""
  String pkgPath = sh(label: "尝试获取War部署包文件", script: "ls ${basePath}/target/*.war 2>/dev/null | head -n 1", returnStdout: true).trim()
  if (pkgPath.endsWith(".war")) {
    pkg = "war"
  } else {
    //注意要过滤掉源码jar包
    pkgPath = sh(label: "尝试获取Jar部署包文件", script: "ls ${basePath}/target/*.jar 2>/dev/null | grep -v 'sources.jar' | head -n 1", returnStdout: true).trim()
    if (pkgPath.endsWith(".jar")) {
      pkg = "jar"
    } else {
      error "未找到部署的包类型，目前只支持jar和war"
    }
  }
  buildDockerImage(pkgPath, pkg)
}
