BuildParam VAR_BUILD_PARAM = null
def agentLabel = "k8s-publish"


pipeline {

  agent {
    node {
      label agent<PERSON>abel
    }
  }
  parameters {
    string(name: 'cluster', defaultValue: '', description: 'k8s集群')
    string(name: 'namespace', defaultValue: '', description: '名称空间')
    string(name: 'pod', defaultValue: '', description: 'pod名称')
    string(name: 'jars', defaultValue: '', description: 'jar包，多个之间用逗号分隔')
    string(name: 'gitUrl', defaultValue: '', description: 'Git地址')
    string(name: 'gitModule', defaultValue: '', description: 'Git模块')
    string(name: 'gitTag', defaultValue: '', description: 'Git的分支或tag')
    string(name: 'publishServer', defaultValue: '', description: '发布系统访问地址')

  }

  stages {
    stage('解析参数') {
      steps {
        script {
          try {
            VAR_BUILD_PARAM = ParseBuildParam()
            printJsonObj(VAR_BUILD_PARAM, "打印解析后的结构化参数")
          } catch (Exception e) {
            error "参数解析错误：" + e.message
          }
          if (VAR_BUILD_PARAM.pod == ''
              || VAR_BUILD_PARAM.gitUrl == '') {
            error "一些必填参数为空"
          }
        }
      }
    }
    stage('拉取Git代码') {
      steps {
        dir(VAR_BUILD_PARAM.pod) {
          script {
            fsEcho "--- ${VAR_BUILD_PARAM.gitUrl} ---"
            checkout([$class                           : 'GitSCM',
                      branches                         : [[name: "${VAR_BUILD_PARAM.gitTag}"]],
                      doGenerateSubmoduleConfigurations: false,
                      extensions                       : [[$class: 'PruneStaleBranch'], [$class: 'RelativeTargetDirectory', relativeTargetDir: "git"]],
                      submoduleCfg                     : [],
                      userRemoteConfigs                : [[credentialsId: '00000000-0000-0000-0000-111111111111', url: "${VAR_BUILD_PARAM.gitUrl}"]]])
          }
        }
      }
    }
    stage('生成exec文件') {
      steps {
        dir(VAR_BUILD_PARAM.pod) {
          script {
            sh "rm -rf classes exec source jars"
            String sourceDir = "git/src/main/java"
            if (!VAR_BUILD_PARAM.gitModule.isEmpty()) {
              sourceDir = "git/${VAR_BUILD_PARAM.gitModule}/src/main/java"
            }
            if (fileExists(sourceDir)) {
              sh "cp -r ${sourceDir} source"
            } else {
              echo "skip copy module source, source directory not exist"
            }
            def downloadUrl = "${VAR_BUILD_PARAM.publishServer}/api/jacoco/dump/download?cluster=${VAR_BUILD_PARAM.cluster}&namespace=${VAR_BUILD_PARAM.namespace}&pod=${VAR_BUILD_PARAM.pod}&jars=${VAR_BUILD_PARAM.jars}"
            def status = sh(label: "创建jacoco报告文件", script: "wget --timeout=1200 --tries=3 '${downloadUrl}' -O jacoco-dump.tar.gz", returnStatus: true)
            if (status != 0) {
              sh 'cat jacoco-dump.tar.gz'
              error "jacoco报告文件创建失败"
            }
            sh "tar -xvf jacoco-dump.tar.gz"
            //todo: 尝试改用maven获取jar的真实源码
            getSourceByDecompiler()
          }
        }
      }
    }
    stage('生成报告') {
      steps {
        dir(VAR_BUILD_PARAM.pod) {
          script {
            jacoco classPattern: 'classes', execPattern: 'exec/*.exec', sourcePattern: 'source/**'
          }
        }
      }
    }
  }
}


def BuildParam ParseBuildParam() {
  def ret = new BuildParam()
  ret.cluster = "${params.cluster}"
  ret.namespace = "${params.namespace}"
  ret.pod = "${params.pod}"
  ret.jars = "${params.jars}"
  ret.gitUrl = "${params.gitUrl}"
  ret.gitModule = "${params.gitModule}"
  ret.gitTag = "${params.gitTag}"
  ret.publishServer = "${params.publishServer}"
  return ret
}

class BuildParam {
  String cluster
  String namespace
  String pod
  String jars
  String gitUrl
  String gitModule
  String gitTag
  String publishServer
}

def printJsonObj(Object obj, String title) {
  def tmpF = "/tmp/k8s-pod-manager-jococo/" + System.currentTimeMillis() + ".json"
  writeJSON file: tmpF, json: obj, pretty: 2
  echo "${title}"
  sh "cat ${tmpF}"
}

/**
 * 自带的echo无法直接展现输出内容
 * 参考：https://stackoverflow.com/questions/49564140/how-can-i-force-jenkins-blue-ocean-to-display-print-output-instead-of-print-mes
 * @param message
 * @return
 */
def fsEcho(message) {
  sh(
      script: "echo $message",
      label: message
  )
}

/**
 * 通过反编译获取源码
 */
def getSourceByDecompiler() {
  def jars = sh(
      script: "ls -1 jars/",
      returnStdout: true
  ).trim() as String
  if (!jars.isEmpty()) {
    if (!fileExists("/tmp/fs-cfr-0.152.jar")) {
      // fs-cfr-0.152.jar 在官方包（cfr-0.152.jar）的基础上做了自定义修改，Owner：@龚春如
      // 修改内容：去掉了反编译类里的Top Header注释，解决覆盖率测试报告里代码行关联不正确的问题
      // 修改位置：org.benf.cfr.reader.entities.classfilehelpers.AbstractClassFileDumper.dumpTopHeader()
      sh "wget https://static.foneshare.cn/oss/cicd/fs-cfr-0.152.jar -O /tmp/fs-cfr-0.152.jar"
    }
    sh "java -jar /tmp/fs-cfr-0.152.jar jars/*.jar --comments false  --showversion false --override --outputdir source"
  }
}

/**
 * 通过maven获取源码
 * todo
 */
def getSourceByMaven() {
  //https://maven.apache.org/plugins/maven-dependency-plugin/index.html
  //mvn  dependency:copy-dependencies -DoverWriteReleases=true -DoverWriteSnapshots=true -DoverWriteIfNewer=true -Dclassifier=resource -DincludeArtifactIds=fs-annotations  -DoutputDirectory=D:\idea_workspace\db-load-metrics\aaa
}
