// Jenkinsfile (Declarative Pipeline)

import groovy.json.JsonOutput
import groovy.json.JsonSlurper

pipeline {
    agent {
        node{
            label 'k8s-publish'
            customWorkspace "/data/jenkins_workspace/k8s-jacoco-unittest/${env.app}"
        }
    }
    options {
        timeout(time: 4, unit: 'HOURS')
        timestamps()
        buildDiscarder(logRotator(numToKeepStr: '15', daysToKeepStr: '10'))
    }
    stages {
        stage('GitSCM') {
            steps{
                script {
                    checkout([$class: 'GitSCM',
                              branches: [[name: "${env.ref}"]],
                              doGenerateSubmoduleConfigurations: false,
                              extensions: [
                                      [$class: 'CleanCheckout'],
                                      [$class: 'LocalBranch', localBranch: "${env.ref}"]
                              ],
                              userRemoteConfigs: [
                                      [credentialsId: '00000000-0000-0000-0000-111111111111', url: "${env.gitUrl}"]
                              ]]
                    )
                }
            }
        }
        stage('RunUnitTest') {
            steps {
                script{
                    def label = getMvnImage()
                    println("label: " + label)
                    def mvnCmd = "mvn -B -fae -am -e -U clean test -Dmaven.javadoc.skip --settings /usr/share/maven/ref/settings.xml"
                    if (env.module) {
                        mvnCmd += " -pl ${env.module}"
                    }
                    mvnCmd += " -Dmaven.wagon.http.ssl.insecure=true -Dserver -Dhttps.protocols=TLSv1.2  -Dorg.slf4j.simpleLogger.showDateTime=true -Dorg.slf4j.simpleLogger.dateTimeFormat=HH:mm:ss.SSS -Dmaven.test.skip=false -Dmaven.test.failure.ignore=true"
                    String hostHostname = sh(script: "hostname", returnStdout: true).trim()
                    String hostSuffix = hostHostname.contains("firstshare") ? "firstshare.cn" : "foneshare.cn"
                    String mavenImage = "reg.${hostSuffix}/base/${label}"
                    sh(label: 'image pull', script: "docker pull ${mavenImage} || true")
                    docker.image("${mavenImage}").inside('--privileged -v $HOME/.m2:/root/.m2 --network=host -h maven.' + hostSuffix + ' -e MAVEN_OPTS="-Xmx8192m -XX:-UseGCOverheadLimit"' + " -e MAVEN_REPO_HOST=maven.${hostSuffix}") {
                        sh(label: 'mvn deploy', script: mvnCmd)
                    }
                }
            }
            post {
                always {
                    junit '**/target/surefire-reports/*.xml'
                    step([$class: 'JacocoPublisher',
                          execPattern: '**/*.exec',
                          classPattern: '**/classes',
                    ])
                }
            }
        }
        stage('ZipFile') {
            steps{
                script {
                    def last_commit = execute("git log -n1 --format='%H'")
                    sh "echo ${last_commit} > commitID.txt"
                    sh "echo ${env.ref} > gitRef.txt"
                    String shell = """
                        if [ -d "src" ] && [ -d "target" ]; then
                          if [ -f "target/jacoco.exec" ]; then
                            mkdir -p "${env.app}"
                            cp -rf target/classes target/jacoco.exec ${env.app}/
                            zip -qr jacoco.zip commitID.txt gitRef.txt ${env.app}
                          fi
                        else
                          appended_values=""
                          for dir in \$(find . -type d); do
                            if [ -f "\$dir/target/jacoco.exec" ]; then
                              cp -rf \$dir/target/classes \$dir/target/jacoco.exec \$dir/
                              appended_values="\$appended_values \$dir/jacoco.exec \$dir/classes/**"
                            fi
                          done
                          zip -rq jacoco.zip commitID.txt gitRef.txt \$appended_values
                        fi
                    """
                    sh "echo ${shell} > command.sh"
                    sh 'chmod +x command.sh'
                    sh './command.sh'
                }

            }
        }
        stage('SendRequest') {
//            when { expression { return fileExists ('jacoco.zip') } }
            steps {
                script{
                    println("开始上传报告")
                }
            }
            post {
                always {
                    script {
                        println("currentBuild.result: " + currentBuild.result)
                        def zip_file = "${env.WORKSPACE}/jacoco.zip"
                        if (fileExists(zip_file)) {
                            String build_url = env.BUILD_URL
                            println("build_url: " + build_url)
                            def case_info = getCaseCount()
                            String ip = "oss.foneshare.cn"
                            if (build_url.contains("firstshare")) {
                                if (env.fs_env == "112") {
                                    ip = "************:32882"
                                }
                            }
//                            String ip = build_url.contains("firstshare") ? "**************:32882" : "************:13028"
                            def url = "http://${ip}/fs-coverage-web/outapi/coverage/completeWithUnittest?appName=${env.app}&gitUrl=${env.gitUrl}&tests=${case_info.tests}&errors=${case_info.errors}&error_list=${case_info.error_list}"
                            def body = JsonOutput.toJson(
                                    appName: env.app,
                                    gitUrl: env.gitUrl,
                                    buildResult: currentBuild.result
                            )
                            println(body)

                            def response = httpRequest httpMode: 'POST', url: url, formData: [
                                    [contentType: 'application/json', name: 'model', body: "${body}"],
                                    [contentType: 'zip', name: 'file', fileName: 'jacoco.zip',
                                     uploadFile: "${zip_file}"]]
                            println("response:")
                            println(response.content)
                        } else {
                            return error('该服务未产生有效的jacoco.exec,请检查pom配置调整后重试，有问题请联系张秋红')
                        }
                    }
                    buildDescription  "app: ${env.app} \n branch: ${env.ref}"
                }
            }
        }
    }
}
String execute(shell) {
    return sh(returnStdout: true, script: shell).toString().trim()
}

def getJavaVersion() {
    def version = 8
    def pom_path = "${env.WORKSPACE}/pom.xml"
    println("pom_path: " + pom_path)
    def out = sh(script: "ls ${pom_path}", returnStatus:true)
    println("out: " + out)
    if(out == 0){
        def result = execute("cat ${pom_path}")
        println("result: ")
        println(result.toString())
        def match = result.toString() =~ /source>(.*.)<\/maven/
        version = match ? match[0][1] : 8
    }
    return version
}
def getMvnImage() {
    try {
        if (env.label) {
            return env.label
        } else {
            def content = execute("cat ${env.WORKSPACE}/.gitlab-ci.yml")
            def match = content =~ /(jdk(.*.)\n)/
            def image =  match ? "openjdk" + (match[0][2]).toString().replace('-', '') : getK8sImage()
            return "fs-maven3.9:" + image
        }

    } catch (err) {
        return getK8sImage()
    }
}
def getK8sImage() {
    def label = null
    def response = httpRequest url: "https://k8s-app.foneshare.cn/api/v1/app/git-modules?app=${env.app}",
            httpMode: 'GET',
            acceptType: 'APPLICATION_JSON',
            customHeaders: [
                    [name: 'Cookie', value: "fs_k8s_app_manager_token=18441cf3-53b1-49b8-af44-5977bb28f5ea--20240531105725"],
            ],
            validResponseCodes: '200'
    def parser = new JsonSlurper()
    def content = parser.parseText(response.content)
    println("content: " + content)
    if (content && content['data']) {
        label = content['data'][0]['mavenImage'].split(':')[1]
        println(label)
    }
    if (label == null) {
        def java_version = getJavaVersion()
        println("java_version: " + java_version)
        label = "openjdk${java_version}"
    }
    return label
}
def getCaseCount() {
    // import groovy.util.XmlParser
    def files  = execute("find . -name 'TEST-*.xml'").split('\n')
    def dic = [tests: 0, errors: 0]
    def error_list = []
    files.each {
        def xmlFile = readFile(it)
        def root = new XmlParser().parseText(xmlFile)
        dic.tests += Integer.parseInt(root.attribute('tests').toString())
        def errors = Integer.parseInt(root.attribute('errors').toString())
        def failures = Integer.parseInt(root.attribute('failures').toString())
        def error_total = errors + failures
        dic.errors += error_total
        if (!['fs-marketing-provider', 'fs-crm-fmcg-service', 'fs-crm-manufacturing'].contains(env.app)) {
            if (error_total > 0) {
                root.depthFirst().findAll() {
                    if (["error", "failure"].contains(it.name())) {
                        error_list.add(it.parent().attribute('classname'))
                    }
                }
            }
        }
    }
    error_list = error_list.toSet().toList()
    dic.error_list = error_list.join(',')
    return dic
}