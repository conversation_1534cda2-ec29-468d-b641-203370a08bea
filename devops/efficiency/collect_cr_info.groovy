// Jenkinsfile (Declarative Pipeline)

import groovy.json.JsonOutput
import groovy.json.JsonSlurper

excluded_patterns = [
    'autotest',
    'devops',
    'demo',
    'test',
    'i18n',
    'crm-doc',
    'admin-system',
    'jjytest',
    'itest'
]
pipeline {
    agent none
    options {
        timeout(time: 4, unit: 'HOURS')
        timestamps()
    }
    environment {
        HOST = 'https://git.firstshare.cn'
    }
    stages {
        stage('GetGitInfo') {
            steps {
                script{
                    def payload = readJSON text: "${env.payload}"
                    println(payload)
                    save_cr_data(payload)
                    save_cr_comment(payload)
                }
            }
        }
    }
}



// 时间转时间戳
def datetimeToStamp(timeStr) {
    try {
        // 尝试解析 ISO 8601 格式 (yyyy-MM-dd'T'HH:mm:ss.SSSXXX)
        def format1 = new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        return format1.parse(timeStr).time
    } catch (Exception e1) {
        try {
            // 尝试解析 GitLab 格式 (yyyy-MM-dd HH:mm:ss Z)
            def format2 = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss Z")
            return format2.parse(timeStr).time
        } catch (Exception e2) {
            try {
                // 尝试解析没有毫秒的 ISO 格式 (yyyy-MM-dd'T'HH:mm:ssXXX)
                def format3 = new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX")
                return format3.parse(timeStr).time
            } catch (Exception e3) {
                println("无法解析日期: ${timeStr}, 错误: ${e3.message}")
                // 返回当前时间作为后备方案
                return System.currentTimeMillis()
            }
        }
    }
}


def getUserId(id){
    def response = callGitlab("users/${id}")
    if (response){
        String dn = response['identities'][0]['extern_uid'].toString().replace(' ', '%20')
        return get('http://oss.firstshare.cn/user/find/ldap?dn=' + dn, [])['employeeId']
    }
    return ''
}

def post(url, body) {
    def response = httpRequest url: url,
            httpMode: "POST",
            contentType: 'APPLICATION_JSON_UTF8',
            requestBody: body,
            ignoreSslErrors: true,
            responseHandle: 'NONE',
            wrapAsMultipart: false
    return response
}

def get(url, headers=null) {
    def response = httpRequest url: url,
            httpMode: 'GET',
            acceptType: 'APPLICATION_JSON',
            customHeaders: headers
    validResponseCodes: '200'
    def content = readJSON text: response.content
//    println(content)
    return content
}

def callGitlab(path) {
    def customHeaders = [
            [name: 'PRIVATE-TOKEN', value: '**************************'],
            [name: 'Content-Type', value: 'application/json']
    ]
    return get("${env.HOST}/api/v4/${path}", customHeaders)
}

def callQat(path, params){
    def body = JsonOutput.toJson(params)
    println("保存数据: ${body}")
    def response = post("http://oss.foneshare.cn/qat/efficiency/${path}", body)
    println("保存结果: ${response.content}")
}

def save_cr_data(payload){
    def params = [
        mr_id__c: (payload.object_attributes.iid).toString(),
        gitUrl__c: payload.project.git_http_url,
        submission_time__c: datetimeToStamp(payload.object_attributes.created_at),
        submitter__c: [(getUserId(payload.object_attributes.author_id)).toString()],
        status__c: payload.object_attributes.merge_status,
    ]
    println("save_cr_data_params: ${params}")
    callQat("save_cr_data", params)
}

def save_cr_comment(payload){
    notes = callGitlab("projects/${payload.project.id}/merge_requests/${payload.object_attributes.iid}/notes")
    notes.each { note ->
        println("note: ${note}")
        if (!note.system){
            def category = 'other'
            if (note.body.contains('PR Code Suggestions')){
                category = 'PR Code Suggestions'
            } else if (note.body.contains('PR Reviewer Guide')){
                category = 'PR Reviewer Guide'
            }
            // 检查是否是AI代码审查员的评论
            boolean isAIReviewer = note.author?.username == 'AI-Code-Reviewer' || 
                                  note.author?.name?.contains('AI') || 
                                  note.body?.contains('PR Code Suggestions')
            
            // 判断评论中是否发现了问题
            boolean issueFound = false
            String impactLevel = ''
            
            if (isAIReviewer) {
                // 检查评论中是否包含"issue", "problem", "bug"等关键词
                issueFound = note.body.toLowerCase().contains('issue') || 
                             note.body.toLowerCase().contains('problem') || 
                             note.body.toLowerCase().contains('bug') ||
                             note.body.toLowerCase().contains('suggestion')
                
                // 提取Impact级别
                if (note.body.contains('High')) {
                    impactLevel = 'High'
                } else if (note.body.contains('Medium')) {
                    impactLevel = 'Medium'
                } else if (note.body.contains('Low')) {
                    impactLevel = 'Low'
                }
                
                println("AI代码审查员发现问题: ${issueFound}, 影响级别: ${impactLevel}")
            }
            
            def params = [
                mr_id__c: payload.object_attributes.iid,
                gitUrl__c: payload.project.git_http_url,
                comment_content__c: note.body,
                comment_time__c: datetimeToStamp(note.created_at),
                commenter__c: note.author.name,
                comment_category__c: category,
                is_issue_found__c: issueFound ? 'true' : 'false',
                issue_level__c: impactLevel
            ]
            println("save_cr_comment_params: ${params}")
            callQat("save_cr_comment_data", params)
        }
    }
}