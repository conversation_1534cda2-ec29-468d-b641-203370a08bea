// Jenkinsfile (Declarative Pipeline)

import groovy.json.JsonOutput
import groovy.json.JsonSlurper

import java.text.SimpleDateFormat

excluded_patterns = [
    'autotest',
    'devops',
    'demo',
    'test',
    'i18n',
    'crm-doc',
    'admin-system',
    'jjytest',
    'itest'
]
pipeline {
    agent none
    options {
        timeout(time: 4, unit: 'HOURS')
        timestamps()
    }
    environment {
        HOST = 'https://git.firstshare.cn'
    }
    stages {
        stage('GetGitInfo') {
            steps {
                script{
                    def payload = readJSON text: "${env.payload}"
                    println(payload)
                    if (payload['commits']) {
                        // 获取git地址
                        def git_url = payload['project']['git_http_url']
                        println("Git URL: ${git_url}")
                        
                        // 检查git地址是否包含excluded_patterns中的任一模式
                        def shouldSkip = false
                        for (pattern in excluded_patterns) {
                            if (git_url.contains(pattern)) {
                                println("Git URL contains excluded pattern '${pattern}', skipping execution")
                                shouldSkip = true
                                break
                            }
                        }
                        
                        // 如果不需要跳过，则继续执行
                        if (!shouldSkip) {
                            def project_id = payload['project_id']
                            def url = "projects/${project_id}/repository/commits/${payload['checkout_sha']}"
                            def commits = callGitlab(url)
                            url = url + "/diff"
                            def diffs = callGitlab(url)
                            // 处理并保存数据
                            saveGitlabData(commits, diffs, payload)
                        
                        } else {
                            println("Execution skipped due to excluded pattern match in git URL")
                        }
                    }
                    
                }
            }
        }
    }
}

def saveGitlabData(commits, diffs, payload) {
    // 计算代码统计数据
    def codeStats = calculateCodeStats(diffs, commits, payload)
    println("codeStats: ${codeStats}")

    // 计算总的统计数据
    def totalStats = [
        'total_added': 0, 'total_deleted': 0,
        'total_lines': 0, 'total_blank_lines': 0,
        'total_comment_lines': 0, 'total_code_lines': 0
    ]

    // 简化提交类别判断逻辑
    def categoryMapping = [
        '业务逻辑': 'option_business_code__c',
        '测试代码': 'option_test_code__c',
        '配置变更': 'option_configuration_file__c',
        '其它': 'other'
    ]

    categoryMapping.each { category, option ->
        if (codeStats[category]['added'] > 0 || codeStats[category]['deleted'] > 0 || codeStats[category]['total_lines'] > 0) {
            def email = commits['committer_email']
            def username = commits['committer_name']
            def dic = [
                    'git_url': payload['project']['git_http_url'],
                    'user_id': getUserId(payload['user_id']),
                    'committer_name': commits['committer_name'],
                    'field_2vk0l__c': codeStats[category]['added'],  // 新增总行数
                    'field_2Gzzh__c': codeStats[category]['added'] + codeStats[category]['deleted'],  // 修改总行数
                    'field_I18er__c': codeStats[category]['deleted'],  // 删除总行数
                    'field_g2fBJ__c': email,  // 提交人邮箱
                    'field_5cDOY__c': username,  // 提交人用户名
                    'field_C1mxo__c': datetimeToStamp(commits['committed_date']),  // 提交时间
                    'owner': ['-10000'],
                    'field_BUbIm__c': payload['checkout_sha'],
                    'field_sqI4v__c': payload['project']['git_http_url'],
                    'field_zFT6V__c': payload['ref'],  // 默认分支
                    // 添加提交类别
                    'submission_category__c': [option],
                    'submit_detail_address__c': commits['web_url'],
                    // 该类别的详细统计字段
                    'category_name__c': category,  // 类别名称
                    'total_lines_in_category__c': codeStats[category]['total_lines'],  // 该类别本次提交涉及的总行数
                    'empty_lines_count__c': codeStats[category]['blank_lines'],  // 该类别本次提交涉及的空行数
                    'comment_lines__c': codeStats[category]['comment_lines'],  // 该类别本次提交涉及的注释行数
                    'code_lines_in_category__c': codeStats[category]['code_lines']  // 该类别本次提交涉及的代码行数
            ]

            // 保存数据
            def body = JsonOutput.toJson(dic)
            println("保存${category}类别数据: ${body}")
            def response = post("http://oss.foneshare.cn/qat/efficiency/save_gitlab_data", body)
            println("保存${category}结果: ${response.content}")

            // 打印该类别的详细统计信息
            println("=== ${category}类别统计详情 ===")
            println("变更统计: 新增${codeStats[category]['added']}行, 删除${codeStats[category]['deleted']}行")
            println("本次提交涉及的行统计:")
            println("  总行数: ${codeStats[category]['total_lines']}行")
            println("  代码行: ${codeStats[category]['code_lines']}行")
            println("  空行: ${codeStats[category]['blank_lines']}行")
            println("  注释行: ${codeStats[category]['comment_lines']}行")
            println("========================")
        }
    }

    // 打印总体统计信息
    if (totalStats['total_added'] > 0 || totalStats['total_deleted'] > 0) {
        println("=== 总体提交统计 ===")
        println("总变更统计: 新增${totalStats['total_added']}行, 删除${totalStats['total_deleted']}行")
        println("本次提交涉及的行统计:")
        println("  总行数: ${totalStats['total_lines']}行")
        println("  代码行: ${totalStats['total_code_lines']}行")
        println("  空行: ${totalStats['total_blank_lines']}行")
        println("  注释行: ${totalStats['total_comment_lines']}行")
        println("==================")
    }
}

// 计算代码统计数据
def calculateCodeStats(diffs, commits, payload) {
    println("开始执行calculateCodeStats")
    def codeStats = [
        '业务逻辑': ['added': 0, 'deleted': 0, 'total_lines': 0, 'blank_lines': 0, 'comment_lines': 0, 'code_lines': 0],
        '测试代码': ['added': 0, 'deleted': 0, 'total_lines': 0, 'blank_lines': 0, 'comment_lines': 0, 'code_lines': 0],
        '配置变更': ['added': 0, 'deleted': 0, 'total_lines': 0, 'blank_lines': 0, 'comment_lines': 0, 'code_lines': 0],
        '其它': ['added': 0, 'deleted': 0, 'total_lines': 0, 'blank_lines': 0, 'comment_lines': 0, 'code_lines': 0]
    ]

    diffs.each { diff ->
        println("diff111: ${diff}")
        if (!(diff instanceof Map)) {
            println("警告: diff 项不是 Map 类型: ${diff?.getClass()}, 值: ${diff}")
            return
        }

        def path = diff['new_path']
        if (!path) {
            println("警告: 发现无效的 diff 项: ${diff}")
            return
        }

        // 计算diff中的添加和删除行数
        def diffContent = diff['diff'] ?: ''
        def diffStats = analyzeDiffLines(diffContent)

        // 分析本次提交中涉及的行（只统计变更的行，不统计整个文件）
        def commitLineStats = analyzeCommitLines(diffContent, path)

        println("处理文件: ${path}")
        println("  变更: 新增${diffStats.added}行, 删除${diffStats.deleted}行")
        println("  本次提交涉及的行: 总计${commitLineStats.total_lines}行 (代码${commitLineStats.code_lines}行, 空行${commitLineStats.blank_lines}行, 注释${commitLineStats.comment_lines}行)")

        // 根据文件类型分类统计
        def category = getFileCategory(path)
        codeStats[category]['added'] += diffStats.added
        codeStats[category]['deleted'] += diffStats.deleted
        codeStats[category]['total_lines'] += commitLineStats.total_lines
        codeStats[category]['blank_lines'] += commitLineStats.blank_lines
        codeStats[category]['comment_lines'] += commitLineStats.comment_lines
        codeStats[category]['code_lines'] += commitLineStats.code_lines
    }

    println("代码统计结果: ${codeStats}")
    return codeStats
}

// 分析diff内容中的添加和删除行数
static def analyzeDiffLines(diffContent) {
    def stats = [added: 0, deleted: 0]

    diffContent.split('\n').each { line ->
        if (line.startsWith('+') && !line.startsWith('+++')) {
            stats.added++
        } else if (line.startsWith('-') && !line.startsWith('---')) {
            stats.deleted++
        }
    }

    return stats
}

// 分析本次提交中涉及的行（只统计变更的行）
def analyzeCommitLines(diffContent, filePath) {
    def stats = [
        total_lines: 0,
        blank_lines: 0,
        comment_lines: 0,
        code_lines: 0
    ]

    def inMultiLineComment = false
    def fileExtension = getFileExtension(filePath)

    println("分析提交涉及的行: ${filePath}, 扩展名: ${fileExtension}")

    diffContent.split('\n').each { line ->
        // 只分析新增的行（以+开头但不是+++的行）
        if (line.startsWith('+') && !line.startsWith('+++')) {
            stats.total_lines++
            def content = line.substring(1) // 去掉 '+' 前缀

            def lineType = getLineType(content, fileExtension, inMultiLineComment)

            if (lineType.isMultiLineComment) {
                inMultiLineComment = lineType.inMultiLineComment
            }

            if (lineType.isBlank) {
                stats.blank_lines++
                println("发现新增空行: '${content}'")
            } else if (lineType.isComment) {
                stats.comment_lines++
                println("发现新增注释行: '${content.trim()}'")
            } else {
                stats.code_lines++
                println("发现新增代码行: '${content.trim()}'")
            }
        }
    }

    println("提交涉及的行统计 ${filePath}: 总行数=${stats.total_lines}, 空行=${stats.blank_lines}, 注释行=${stats.comment_lines}, 代码行=${stats.code_lines}")

    return stats
}

// 分析文件完整内容
def analyzeFileContent(filePath, commitId, payload) {
    try {
        // 获取文件内容
        def projectId = payload['project_id']
        def encodedPath = URLEncoder.encode(filePath, "UTF-8").replace("+", "%20")
        def url = "projects/${projectId}/repository/files/${encodedPath}/raw?ref=${commitId}"

        println("获取文件内容: ${url}")
        def fileContent = callGitlabRaw(url)

        if (!fileContent) {
            println("无法获取文件内容: ${filePath}")
            return null
        }

        return analyzeFileLines(fileContent, filePath)

    } catch (Exception e) {
        println("获取文件内容失败: ${filePath}, 错误: ${e.message}")
        return null
    }
}

// 分析文件行内容
def analyzeFileLines(fileContent, filePath) {
    def stats = [
        total_lines: 0,
        blank_lines: 0,
        comment_lines: 0,
        code_lines: 0
    ]

    def lines = fileContent.split('\n')
    def inMultiLineComment = false
    def fileExtension = getFileExtension(filePath)

    lines.each { line ->
        stats.total_lines++

        def lineType = getLineType(line, fileExtension, inMultiLineComment)

        if (lineType.isMultiLineComment) {
            inMultiLineComment = lineType.inMultiLineComment
        }

        if (lineType.isBlank) {
            stats.blank_lines++
        } else if (lineType.isComment) {
            stats.comment_lines++
        } else {
            stats.code_lines++
        }
    }

    return stats
}



// 获取文件扩展名
static def getFileExtension(filePath) {
    def lastDot = filePath.lastIndexOf('.')
    return lastDot > 0 ? filePath.substring(lastDot + 1).toLowerCase() : ''
}

// 根据文件路径确定分类
static def getFileCategory(path) {
    if (path.contains('/test/')) {
        return '测试代码'
    } else if (path.endsWith('.xml') || path.endsWith('.properties') || path.endsWith('.yml') || path.endsWith('.yaml')) {
        return '配置变更'
    } else if (path.endsWith('.java') || path.endsWith('.js') || path.endsWith('.vue')) {
        return '业务逻辑'
    } else {
        return '其它'
    }
}

// 分析单行的类型（空行、注释行、代码行）
static def getLineType(content, fileExtension, inMultiLineComment) {
    def trimmed = content.trim()
    def result = [
        isBlank: false,
        isComment: false,
        isCode: false,
        isMultiLineComment: false,
        inMultiLineComment: inMultiLineComment
    ]

    // 检查空行
    if (trimmed.isEmpty()) {
        result.isBlank = true
        return result
    }
    // 根据文件类型检查注释
    switch (fileExtension) {
        case 'java':
        case 'js':
        case 'vue':
        case 'ts':
        case 'jsx':
        case 'tsx':
            result = analyzeJavaStyleComments(trimmed, inMultiLineComment)
            break
        case 'xml':
        case 'html':
        case 'htm':
            result = analyzeXmlComments(trimmed, inMultiLineComment)
            break
        case 'py':
            result = analyzePythonComments(trimmed)
            break
        case 'properties':
        case 'yml':
        case 'yaml':
            result = analyzeConfigComments(trimmed)
            break
        default:
            // 默认按Java风格处理
            result = analyzeJavaStyleComments(trimmed, inMultiLineComment)
    }

    // 如果不是空行也不是注释，则是代码行
    if (!result.isBlank && !result.isComment) {
        result.isCode = true
    }

    return result
}

// 分析Java风格注释 (// 和 /* */)
static def analyzeJavaStyleComments(trimmed, inMultiLineComment) {
    def result = [
        isBlank: false,
        isComment: false,
        isCode: false,
        isMultiLineComment: false,
        inMultiLineComment: inMultiLineComment
    ]

    // 如果当前在多行注释中
    if (inMultiLineComment) {
        result.isComment = true
        result.isMultiLineComment = true
        // 检查是否结束多行注释
        if (trimmed.contains('*/')) {
            result.inMultiLineComment = false
        } else {
            result.inMultiLineComment = true
        }
        return result
    }

    // 检查单行注释
    if (trimmed.startsWith('//')) {
        result.isComment = true
        return result
    }

    // 检查多行注释开始
    if (trimmed.startsWith('/*')) {
        result.isComment = true
        result.isMultiLineComment = true
        // 检查是否在同一行结束
        if (trimmed.contains('*/')) {
            result.inMultiLineComment = false
        } else {
            result.inMultiLineComment = true
        }
        return result
    }

    return result
}

// 分析XML风格注释 (<!-- -->)
static def analyzeXmlComments(trimmed, inMultiLineComment) {
    def result = [
        isBlank: false,
        isComment: false,
        isCode: false,
        isMultiLineComment: false,
        inMultiLineComment: inMultiLineComment
    ]

    // 如果当前在多行注释中
    if (inMultiLineComment) {
        result.isComment = true
        result.isMultiLineComment = true
        if (trimmed.contains('-->')) {
            result.inMultiLineComment = false
        } else {
            result.inMultiLineComment = true
        }
        return result
    }

    // 检查XML注释
    if (trimmed.startsWith('<!--')) {
        result.isComment = true
        result.isMultiLineComment = true
        if (trimmed.contains('-->')) {
            result.inMultiLineComment = false
        } else {
            result.inMultiLineComment = true
        }
        return result
    }

    return result
}

// 分析Python风格注释 (#)
static def analyzePythonComments(trimmed) {
    def result = [
        isBlank: false,
        isComment: false,
        isCode: false,
        isMultiLineComment: false,
        inMultiLineComment: false
    ]

    if (trimmed.startsWith('#')) {
        result.isComment = true
    }

    return result
}

// 分析配置文件注释 (#)
static def analyzeConfigComments(trimmed) {
    def result = [
        isBlank: false,
        isComment: false,
        isCode: false,
        isMultiLineComment: false,
        inMultiLineComment: false
    ]

    if (trimmed.startsWith('#')) {
        result.isComment = true
    }

    return result
}

// 时间转时间戳
static def datetimeToStamp(timeStr) {
    def format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    def date = format.parse(timeStr)
    return date.time 

}


def getUserId(id){
    def response = callGitlab("users/${id}")
    if (response){
        String dn = response['identities'][0]['extern_uid'].toString().replace(' ', '%20')
        return get('http://oss.firstshare.cn/user/find/ldap?dn=' + dn, [])['employeeId']
    }
    return ''
}

def post(url, body) {
    def response = httpRequest url: url,
            httpMode: "POST",
            contentType: 'APPLICATION_JSON_UTF8',
            requestBody: body,
            ignoreSslErrors: true,
            responseHandle: 'NONE',
            wrapAsMultipart: false
    return response
}

def get(url, headers=null) {
    def response = httpRequest url: url,
            httpMode: 'GET',
            acceptType: 'APPLICATION_JSON',
            customHeaders: headers
    validResponseCodes: '200'
    def content = readJSON text: response.content
//    println(content)
    return content
}

def callGitlab(path) {
    def customHeaders = [
            [name: 'PRIVATE-TOKEN', value: '**************************'],
            [name: 'Content-Type', value: 'application/json']
    ]
    return get("${env.HOST}/api/v4/${path}", customHeaders)
}

// 调用GitLab API获取原始文件内容
def callGitlabRaw(path) {
    def customHeaders = [
            [name: 'PRIVATE-TOKEN', value: '**************************']
    ]
    try {
        def response = httpRequest url: "${env.HOST}/api/v4/${path}",
                httpMode: 'GET',
                customHeaders: customHeaders,
                validResponseCodes: '200,404'

        if (response.status == 200) {
            return response.content
        } else {
            println("文件不存在或无法访问: ${path}")
            return null
        }
    } catch (Exception e) {
        println("获取原始文件内容失败: ${path}, 错误: ${e.message}")
        return null
    }
}

