import json
import re
import requests
from base64 import b64encode
import javalang
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
import base64
from requests.utils import dict_from_cookiejar


class GitTestAnalyzer:
    def __init__(self):
        self.workspace  = '/Users/<USER>/Documents/PycharmProjects/i18nScan'
        self.sonar_host = "https://oss.firstshare.cn/sonarqube"
        self.fs_host = 'https://www.fxiaoke.com'
        self.session = requests.Session()
        self.user_map = self.get_user_map()
        self.token = self.get_token()
        self.cookies = self.login()
        self.email_map = {}
        self.sonar_token = 'squ_7f458a9a870cafe33bb928d6f44313531d0b4ff2'

    @staticmethod
    def encrypt(text):
        public_key = """-----BEGIN PUBLIC KEY-----
        MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCROXqyCKxG8DrQKvrmdwiAHFJseaLHKsdzJ+61EpEGUawyLk5obn2Z2lyVVGjqT3KECk3DJtAD6Jux/m/gW2/lxspvhUO1YE1P8OZuUq5xhr/3AWuSSXCqLM2q6TEMnI2VE1BzlsRcxQVGVd4kGszzpyLXYS9ubFTTp1C2A+uZ1QIDAQAB
        -----END PUBLIC KEY-----
            """
        rsakey = RSA.importKey(public_key)
        cipher = Cipher_pkcs1_v1_5.new(rsakey)
        cipher_text = base64.b64encode(cipher.encrypt(text.encode()))
        value = cipher_text.decode('utf8')
        return str(value)

    def login(self):
        cookies = {}
        params = {
            'enterpriseAccount': '794608_sandbox',
            'persistenceHint': True,
            'publickKey': "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCROXqyCKxG8DrQKvrmdwiAHFJseaLHKsdzJ+61EpEGUawyLk5obn2Z2lyVVGjqT3KECk3DJtAD6Jux/m/gW2/lxspvhUO1YE1P8OZuUq5xhr/3AWuSSXCqLM2q6TEMnI2VE1BzlsRcxQVGVd4kGszzpyLXYS9ubFTTp1C2A+uZ1QIDAQAB",
            'rsaPassword': self.encrypt(self.token['fs_password']),
            'userAccount': self.token['fs_username']
        }
        url = f"{self.fs_host}/FHH/EM0HUL/Authorize/EnterpriseAccountLogin"
        response = self.session.post(url, json=params)
        if response.status_code == 200:
            cookies = dict_from_cookiejar(response.cookies)
        return cookies
        
    def call_sonarqube(self, url, params=None):
        full_url = self.sonar_host + url
        response = requests.get(
            full_url, 
            params=params, 
            auth=(self.sonar_token, '')
        )   
        try:
            return response.json()
        except:
            return response.text
    
    def extract_groovy_tests(self, content):
        """解析Groovy文件中的测试用例
        
        Args:
            content (str): Groovy文件内容
            
        Returns:
            list: 测试方法信息列表
        """
        test_blocks = []
        lines = content.split('\n')
        
        # 匹配Spock测试方法的正则表达式
        test_patterns = [
            r'(?:@\w+\s+)*def\s+[\'"]([^\'"]+)[\'"]\s*\(\s*\)\s*\{',  # Spock feature方法
            r'(?:@\w+\s+)*def\s+(\w+)\s*\(\s*\)\s*\{'  # 普通方法
        ]
        
        for i, line in enumerate(lines):
            for pattern in test_patterns:
                match = re.match(pattern, line.strip())
                if match:
                    start_line = i + 1
                    test_name = match.group(1)
                    
                    # 使用大括号计数找到正确的结束位置
                    brace_count = 1  # 已经遇到了开始的 {
                    end_line = start_line
                    
                    # 从方法开始行往下扫描
                    for j, next_line in enumerate(lines[start_line:], start_line + 1):
                        brace_count += next_line.count('{')
                        brace_count -= next_line.count('}')
                        
                        if brace_count == 0:
                            end_line = j
                            break
                    
                    test_body = '\n'.join(lines[start_line-1:end_line])
                    
                    # 检查是否是有效的测试方法
                    if self._is_valid_test_method(test_body):
                        test_blocks.append({
                            'name': test_name,
                            'content': test_body,
                            'start': start_line,
                            'end': end_line
                        })
        
        return test_blocks
    
    def _is_valid_test_method(self, test_body):
        """检查是否是有效的测试方法
        
        Args:
            test_body (str): 测试方法体
            
        Returns:
            bool: 是否是有效的测试方法
        """
        # Spock测试关键字
        spock_keywords = [
            'given:', 'when:', 'then:', 'expect:', 'cleanup:', 'where:',
            'and:', 'setup:', 'setupSpec:', 'cleanup:', 'cleanupSpec:'
        ]
        
        # 测试断言关键字
        assertion_keywords = [
            'assert', 'verify', 'expect', 'should', 'must',
            'thrown', 'notThrown', 'with', 'mock', 'stub'
        ]
        
        test_body = test_body.lower()
        
        # 检查是否包含Spock关键字
        has_spock_keyword = any(keyword.lower() in test_body for keyword in spock_keywords)
        
        # 检查是否包含断言关键字
        has_assertion = any(keyword.lower() in test_body for keyword in assertion_keywords)
        
        return has_spock_keyword or has_assertion
    
    def _get_line_number(self, content, position):
        """获取位置对应的行号
        
        Args:
            content (str): 文件内容
            position (int): 字符位置
            
        Returns:
            int: 行号
        """
        return content.count('\n', 0, position) + 1
    

    def extract_junit_tests(self, java_code):
        """提取 JUnit 测试方法，支持 JUnit 4 和 5
        Args:
            java_code: Java源代码
        Returns:
            list: 测试方法信息列表
        """
        tests = []
        try:
            tree = javalang.parse.parse(java_code)
            for path, node in tree.filter(javalang.tree.MethodDeclaration):
                # 支持更多 JUnit 5 注解
                junit_annotations = {
                    'Test',  # JUnit 4 & 5
                    'ParameterizedTest',
                    'RepeatedTest',
                    'TestFactory',
                    'TestTemplate',
                    'DisplayName'
                }
                
                if any(i.name in junit_annotations for i in node.annotations):
                    start_line = node.position.line
                    if node.body and node.body[-1].position:
                        end_line = node.body[-1].position.line
                    else:
                        end_line = start_line + 1
                        
                    tests.append({
                        'name': node.name,
                        'start': start_line,
                        'end': end_line
                    })
                    
        except (javalang.parser.JavaSyntaxError, Exception) as e:
            tests = self._extract_tests_fallback(java_code)
        
        return tests

    def _extract_tests_fallback(self, java_code):
        """备用的测试方法提取方案，支持 JUnit 5"""
        tests = []
        lines = java_code.split('\n')
        in_test = False
        current_test = None
        brace_count = 0
        
        # JUnit 5 测试注解
        test_annotations = {
            '@Test',
            '@ParameterizedTest',
            '@RepeatedTest',
            '@TestFactory',
            '@TestTemplate'
        }
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # 跳过注释
            if line.startswith('//') or line.startswith('/*') or line.startswith('*'):
                continue
            
            # 检测测试注解
            if any(anno in line for anno in test_annotations):
                in_test = True
                continue
            
            # 检测方法声明
            if in_test and 'void' in line:
                match = re.search(r'(?:public|private|protected)?\s*void\s+(\w+)', line)
                if match:
                    current_test = {
                        'name': match.group(1),
                        'start': i
                    }
                    brace_count = line.count('{')
                    continue
                
            if current_test:
                brace_count += line.count('{')
                brace_count -= line.count('}')
                
                if brace_count == 0:
                    current_test['end'] = i
                    tests.append(current_test)
                    current_test = None
                    in_test = False
        
        return tests

    def call_gitlab(self, url, params=None, return_json=False):
        host = "https://git.firstshare.cn"
        headers = {
            'PRIVATE-TOKEN': self.token['gitlab_token'],
            'Content-Type': 'application/json'
        }
        response = self.session.get(host + url, params=params, headers=headers)
        if return_json:
            return response.json()
        return response.text


    def get_project_analyses(self, project_key, branch="main"):
        params = {
            "project": project_key,
            "ps": 1,
            "branch": branch
        }
        url = f"/api/project_analyses/search"
        return self.call_sonarqube(url, params=params)
    
    def get_project_links(self, project_key):
        url = f"/api/project_links/search"
        params = {
            "projectKey": project_key,
        }
        return self.call_sonarqube(url, params=params)

    def get_component_tree(self, project_key, branch="main"):
        url = f"/api/measures/component_tree"
        params = {
            "branch": branch,
            "additionalFields": "metrics",
            "ps": 500,
            "asc": 'true',
            "metricSort": "tests",
            "s": "metric",
            "metricSortFilter": "withMeasuresOnly",
            "component": project_key,
            "metricKeys": "tests",
            "strategy": "leaves"
        }
        return self.call_sonarqube(url, params=params)

    def safe_add(self, dict, key, value):
        if key not in dict:
            dict[key] = 0
        dict[key] += value


    def get_gitlab_project_id(self, git_url):
        try:
            git_url = git_url[:-4]
            # 获取最后一部分作为项目路径
            project_path = '/'.join(git_url.split('/')[-2:])
            # 4. 通过 GitLab API 查询 project ID
            url = f"/api/v4/projects/{project_path.replace('/', '%2F')}"
            response = self.call_gitlab(url, return_json=True)
            return response['id']     
        except Exception as e:
            print(f"Error getting GitLab project ID: {str(e)}")
            return None

    def get_file_blame(self, project_id, file_path, revision=None):
        url = f"/api/v4/projects/{project_id}/repository/files/{file_path.replace('/', '%2F')}/blame"
        params = {'ref': revision}
        response = self.call_gitlab(url, params=params, return_json=True)
        datas = []
        for i in response:
            if len(response) == 1 and '\r' in i['lines'][0]:
                i['lines'] = i['lines'][0].split('\r')
            for d in i['lines']:
                datas.append(
                    {
                        'commit_id': i['commit']['id'], 
                        'author_name': i['commit']['author_name'], 
                        'author_email': i['commit']['author_email'], 
                        'line_content': d
                    })
        return datas

    def get_sources_raw(self, key, branch):
        url = '/api/sources/raw'
        params = {
            'key': key,
            'branch': branch
        }
        response = self.call_sonarqube(url, params=params)
        return response

    def get_unit_test_info(self, project_key, project_id, branch="main", revision=None):
        response = self.get_component_tree(project_key, branch)
        components = response.get("components", [])
        case_info = {}
        total_count = 0
        for i in components:
            key = i.get("key")
            count = int(i['measures'][0]['value'])
            total_count += count
            sources = self.get_file_blame(project_id, key.split(':')[-1], revision)
            java_code = self.get_sources_raw(key, branch)
            if key.endswith('.java'):
                tests = self.extract_junit_tests(java_code)
            else:
                tests = self.extract_groovy_tests(java_code)           
            for test in tests:
                commiters = {}
                for i in sources[test['start'] - 1: test['end']]:
                    commiters[i['author_name']] = i['author_email']
                    self.email_map[i['author_name']] = i['author_email']
                commiters = list(set(commiters.keys()))
                for c in commiters:
                    self.safe_add(case_info, c, 1)
        return case_info

    def search_projects(self):
        projects = []
        for i in range(1, 3):
            params = {"ps": 500, "p": i}
            response = self.call_sonarqube('/api/projects/search', params=params)
            projects += response['components']
        return projects

    def get_project_branches(self, project_key):
        url = f"/api/project_branches/list"
        params = {
            "project": project_key,
        }
        return self.call_sonarqube(url, params=params)

    def call_qat(self, params):
        url = "http://oss.foneshare.cn/qat/efficiency/save_unittest_data"
        response = self.session.post(url, json=params)
        return response.json()
    

    def get_git_url(self, project_key):
        url = ''
        links = self.get_project_links(project_key)['links']
        if links:
            links = list(filter(lambda x: x['type'] == 'scm', links))
            if links:
                url = links[0]['url']
        return url


    def get_config(self, url):
        return eval(self.session.post(url).text)

    def get_user_map(self):
        url = f"http://oss.firstshare.cn/cs/api/get/?token=user_map&profile=fstest&name=user_map"
        return self.get_config(url)

    
    def get_token(self):
        url = f"http://oss.firstshare.cn/cs/api/get/?token=256d59ad031d4d938c5f4c10b3ec3839&profile=fstest&name=zqh_token"
        return self.get_config(url)

    def get_gitlab_branch(self, project_id, branch):
        url = f"/api/v4/projects/{project_id}/repository/branches/{branch.replace('/', '%2F')}"
        response = self.call_gitlab(url, return_json=True)
        return True if response.get('name', None) else False

    def get_cas_id(self, name):
        match = re.match(r'^([a-z]{3,})(\d{1,6})$', name)
        if match:
            letters, numbers = match.groups()
            return numbers
        return None

    def get_user_id(self, name, email):     
        email_name = email.split('@')[0]
        user_id = self.get_cas_id(name) or self.get_cas_id(email_name)
        if user_id:
            return user_id
        letters = re.sub(r'\d', '', name)  # 移除数字
        matches = [x for x in self.user_map if (
            # 匹配邮箱前缀
            (x['email'].split('@')[0] in [email_name, name]) or
            # 匹配用户名
            (x['name'] in [email_name, name]) or
            # 匹配字母部分
            (re.sub(r'\d', '', x['email'].split('@')[0]) == letters)
        )]
        
        if matches:
            return matches[0]['user_id']
        phone = re.findall(r'\d{11}', email_name)
        if phone:
            filters = list(filter(lambda x: x['phone'] == phone[0], self.user_map))
            if filters:
                return filters[0]['user_id']
        response = self.get_users_by_department(name)
        values = response['Value']['employeeList']
        if values:
            return str(values[0]['employeeId'])
        return None

    def get_users_by_department(self, keyword):
        payload = {
            "keyword": keyword,
            "pageSize": 20,
            "pageNumber": 1,
            "departmentId": 1381,
            "employeeProperty": 1,
            "departmentProperty": 1
            }
        url = f"{self.fs_host}/FHH/EM1HORGBIZ/Organization/Employee/SearchContactByDepartment?_fs_token={self.cookies['fs_token']}"
        return self.session.post(url, json=payload, cookies=self.cookies).json()


    def get_personnel_obj(self, field_name=None, value=None):
        payload = {
            "serializeEmpty": False,
            "extractExtendInfo": True,
            "object_describe_api_name": "PersonnelObj",
            "search_template_id": "5e74db162919dd44beb16638",
            "include_describe": False,
            "include_layout": False,
            "need_tag": True,
            "search_template_type": "default",
            "ignore_scene_record_type": False,
            "search_query_info": "{\"limit\":20,\"offset\":0,\"filters\":[{\"operator\":\"EQ\",\"field_name\":\"departmentId\",\"field_values\":[1381]},{\"field_name\":\"%s\",\"field_values\":[\"%s\"],\"operator\":\"LIKE\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}" % (field_name, value),
            "pageSizeOption": [20, 50, 100],
            "departmentId": 1381,
            "management": True,
            "scene_api_name": "All"
        }
        url = f"{self.fs_host}/FHH/EM1HNCRM/API/v1/object/PersonnelObj/controller/List?_fs_token={self.cookies['fs_token']}"
        users = self.session.post(url, json=payload, cookies=self.cookies).json()
        if users['Value']['dataList']:
            return users['Value']['dataList'][0]['user_id']
        return None

    def merge_test_info(self, case_info):
        merged_data = {}  
        for k, v in case_info.items(): 
            email = self.email_map.get(k, None)
            user_id = self.get_user_id(k, email)
            dic = {
                'submitter__c': k,
                'author_name__c': k,
                'unit_test_count__c': v,
                'author_email': email,
                'user_id': user_id
            }
            if user_id is None:
                merged_data[k] = dic
                continue
            if user_id in merged_data:
                merged_data[user_id]['unit_test_count__c'] += v
            else:
                merged_data[user_id] = dic
        return merged_data

    def main(self):   
        projects = self.search_projects()
        for project in projects:
            project_key = project['key']
            git_url = self.get_git_url(project_key)
            if git_url:
                try:
                    project_id = self.get_gitlab_project_id(git_url)
                    branches = self.get_project_branches(project_key)['branches']
                    for branch in branches:
                        branch = branch['name']
                        print(f"开始执行工程分支: {branch}", flush=True)
                        if self.get_gitlab_branch(project_id, branch):
                            response = self.get_project_analyses(project_key, branch)
                            analyses = response['analyses']
                            if analyses:
                                revision = analyses[0]['revision']
                                case_info = self.get_unit_test_info(project_key, project_id, branch, revision)
                                dic = {}
                                if case_info != {}:
                                    dic['branch__c'] = branch
                                    dic['revision__c'] = revision
                                    dic['git_url__c'] = git_url
                                    dic['sonar_link__c'] = project_key
                                    merged_data = self.merge_test_info(case_info)
                                    for data in merged_data.values():
                                        self.call_qat({**dic, **data})
                                    
                except Exception as e:
                    print(f"{project_key}录入失败, Error: {e}", flush=True)
                print(f"录入完成: {project_key}", flush=True)
            else:
                print(f"未找到git_url: {project_key}", flush=True)


if __name__ == "__main__":
    GitTestAnalyzer().main()